<script lang="ts" setup>
import type { IAicConditionDataOptionItem } from '@/types/aic'
import { ref, watch } from 'vue'

const props = defineProps<{
    config: IAicConditionDataOptionItem[]
    onChange: (v: string[]) => void
    value: string[]
}>()

const selectedValue = ref<string[]>([])

const cascaderProps = { multiple: true }

watch(
    () => props.value,
    (value) => {
        if (!value || value.length === 0) selectedValue.value = []
        selectedValue.value = value
    }
)

watch(
    () => selectedValue.value,
    (value) => {
        console.log('selectedValue11', JSON.stringify(value))
        props.onChange(value)
    }
)
</script>

<template>
    <div class="flex flex-row left-right-center gap-8 pointer no-select cascader-select">
        <el-cascader
            v-model="selectedValue"
            :options="config"
            :props="cascaderProps"
            collapse-tags
            collapse-tags-tooltip
            clearable
        />
    </div>
</template>

<style lang="scss" scoped>
.cascader-select :deep(.el-input__wrapper) {
    width: 492px;
}

.cascader-select :deep(.el-input__inner) {
    // height: 26px;
    font-size: 12px;
}

.cascader-select :deep(.el-tag) {
    height: 20px;
}
</style>
