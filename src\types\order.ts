import type { IAllRecord } from '@/types/record'
import type { ContactItem } from '@/types/company'
import type { ICommonResponse } from './axios'

export interface IServiceOrderPageParams extends IAllRecord {
    page: number
    pageSize: number
    serviceKeys?: string | undefined
}
export interface IServiceOrderResponseItemService {
    app_id: string
    created_at: string
    id: number
    service_desc: string
    service_id: number
    service_name: string
    service_type: number
    should_masking: number
    status: number
    unit: string
    unit_quantity: number
    unit_type: number
    updated_at: string
}
export interface IServiceOrderResponseItem {
    app_id: string
    balance: number
    channel_id: number
    created_at: string
    expire_time: string
    id: number
    is_allow_negative: number
    is_expired: boolean
    out_order_id: string
    quantity: number
    service: IServiceOrderResponseItemService
    service_id: number
    unit_type: number
    updated_at: string
    user_id: string
}
export interface IServiceOrderResponse {
    data: IServiceOrderResponseItem[]
    errCode: number
    firstPage: boolean
    hasNextPage: boolean
    hasPreviousPage: boolean
    lastPage: boolean
    page: number
    pageSize: number
    success: boolean
    total: number
    totalPages: number
}
export interface IOrderServiceStatisticsParams extends IAllRecord {
    serviceKeys: string
    tenantId?: string
}

export interface IOrderBuyLegalResponse extends ICommonResponse {
    contacts: ContactItem[]
    contactNum: number
}

export interface IOrderParams extends IAllRecord {
    serviceKey: string
    socialCreditCode: string
    companyName: string
    orderId?: number
}

export interface IOrderCheckEntBuyResponse {
    status: string
    totalBalance: string
    optionId: string
}

export interface IOrderCheckEntBuyResponseArr {
    data: [
        {
            optionId: string
            status: string
            expireTime: string
        }
    ]
}

export interface IOrderCheckEntBuyParams extends IAllRecord {
    socialCreditCode: string
}

export interface IOrderUsageRecordParams extends IAllRecord {
    page: number
    pageSize: number
    hideExpired?: number
    hideValid?: number
    serviceOrderId?: string
}

export interface IOrderUsageRecordResponseItem {
    app_id: string
    belong: number
    change_type: string
    channel_id: number
    created_at: string
    expire_time: string
    extra_info: string
    id: number
    option_id: string
    quantity: number
    service: IServiceOrderResponseItemService
    service_id: number
    service_order_id: number
    start_time: string
    updated_at: string
    user_id: string
    isExpanded?: boolean
}
export interface IOrderUsageRecordResponse {
    data: IOrderUsageRecordResponseItem[]
    errCode: number
    firstPage: boolean
    hasNextPage: boolean
    hasPreviousPage: boolean
    lastPage: boolean
    page: number
    pageSize: number
    success: boolean
    total: number
    totalPages: number
}