<script lang="ts" setup>
import outboundService from '@/service/outboundService'
import type {
    IAutoDialerListItem,
    IAutoDialerTaskDetailFilter,
    IAutoDialerTaskDetailListItem,
    IAutoDialerTaskExportRequest,
} from '@/types/autoDialer'
import moment from 'moment'
import { onMounted, ref, watch } from 'vue'
import CallRecord from '../../dialing-task-detail/components/call-record/CallRecord.vue'
import { downloadFile } from '@/utils/download'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'

// ====================== Interfaces & Types ======================

// ====================== Props & Injections ======================
const props = defineProps<{
    filterParams?: IAutoDialerTaskDetailFilter
    setCurrentTask?: (task: IAutoDialerListItem) => void
}>()

// ====================== Store & Computed Values ======================

// ====================== Refs & Reactive State ======================
const list = ref<IAutoDialerTaskDetailListItem[]>([])
const loading = ref(false)
const pageInfo = ref({
    page: 1,
    pageSize: 20,
    total: 0,
})
const currentTask = ref<IAutoDialerTaskDetailListItem>()
const requestParams = ref<IAutoDialerTaskDetailFilter>({
    phoneNumber: '',
    status: '',
    companyName: '',
    startCallTime: '',
    endCallTime: '',
    aiTagName: '',
})
const callRecordVisible = ref(false)
const multipleSelection = ref<IAutoDialerTaskDetailListItem[]>([])
const exporting = ref(false)
const router = useRouter()

// ====================== Methods ======================
const getData = (params: IAutoDialerTaskDetailFilter) => {
    loading.value = true
    outboundService
        .taskDetailPage({
            page: pageInfo.value.page,
            pageSize: pageInfo.value.pageSize,
            ...params,
        })
        .then((res) => {
            loading.value = false
            const { errCode, data, total } = res
            if (errCode === 0) {
                list.value = data
                pageInfo.value.total = total
            } else {
                list.value = []
            }
        })
        .catch(() => {
            loading.value = false
            list.value = []
            pageInfo.value.total = 0
        })
}

const toRecordsDetail = (row: IAutoDialerTaskDetailListItem) => {
    currentTask.value = row
    callRecordVisible.value = true
}

const exportRecord = (num?: number) => {
    let params: IAutoDialerTaskExportRequest = {}

    if (!num) {
        if (multipleSelection.value.length === 0) {
            return ElMessage.warning('请选择要导出的记录')
        }
        params.ids = multipleSelection.value.map((item) => item.id)
    } else if (num > 0) {
        params = { ...props.filterParams, nums: num }
    }

    if (exporting.value) return
    exporting.value = true
    outboundService
        .taskExport(params)
        .then((res) => {
            exporting.value = false
            const { data } = res
            const { type } = data || {}
            if (type === 'application/vnd.ms-excel') {
                downloadFile(res)
            } else {
                ElMessage.success('您的导出任务已创建，请到任务管理页面查看下载')
            }
        })
        .catch(() => {
            ElMessage.warning('导出失败，请稍后再试')
        })
}

const handleSelectionChange = (val: IAutoDialerTaskDetailListItem[]) => {
    multipleSelection.value = val
}

const toTaskDetail = (item: IAutoDialerListItem) => {
    const route = router.resolve({
        name: 'dialing-task-detail',
        query: {
            taskCode: item.taskCode,
        },
    })
    window.open(route.href, '_blank')
}

// ====================== Watchers ======================
watch(
    () => props.filterParams,
    (value) => {
        if (value) {
            getData(value)
        }
    },
    {
        deep: true,
    }
)

// ====================== Lifecycle Hooks ======================
onMounted(() => {
    getData(requestParams.value)
})
</script>

<template>
    <div class="flex flex-column height-100 dialing-contacts-table">
        <div class="flex flex-1 flex-column" style="overflow: hidden">
            <div class="flex flex-row gap-16 space-between tb-padding-16">
                <div class="flex flex-row gap-16 top-bottom-center">
                    <div class="flex flex-row gap-8 top-bottom-center">
                        <div class="font-14">
                            已选<span class="color-blue lr-padding-2 font-weight-600">{{
                                multipleSelection.length
                            }}</span
                            >个
                        </div>
                    </div>
                    <div class="flex flex-row">
                        <el-dropdown placement="bottom-start">
                            <el-button :loading="exporting" :disabled="exporting">
                                导出
                                <el-icon class="l-margin-8"><CaretBottom /></el-icon>
                            </el-button>
                            <template #dropdown>
                                <el-dropdown-menu>
                                    <el-dropdown-item @click="exportRecord()">导出所选</el-dropdown-item>
                                    <el-dropdown-item @click="exportRecord(100)">导出前100条</el-dropdown-item>
                                    <el-dropdown-item @click="exportRecord(500)">导出前500条</el-dropdown-item>
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>
                    </div>
                </div>
            </div>

            <el-table
                :data="list"
                style="width: 100%"
                v-loading="loading"
                height="100%"
                @selection-change="handleSelectionChange"
                show-overflow-tooltip
                row-key="id"
            >
                <el-table-column type="selection" width="55" fixed="left" />

                <el-table-column prop="taskName" label="任务名称" width="120px">
                    <template #default="scope">
                        <div class="flex h-32 top-bottom-center">
                            <a class="pointer color-blue" @click="toTaskDetail(scope.row)">
                                {{ scope.row.taskName }}
                            </a>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="callStartTime" label="接通时间" width="180px">
                    <template #default="scope">
                        {{
                            scope.row.callStartTime
                                ? moment(scope.row.callStartTime).format('YYYY-MM-DD HH:mm:ss')
                                : '-'
                        }}
                    </template>
                </el-table-column>
                <el-table-column prop="name" label="联系人" width="120px" />
                <el-table-column prop="companyName" label="企业名称" width="250px" />
                <el-table-column prop="tags" label="身份">
                    <template #default="scope">
                        {{ scope.row.tags }}
                    </template>
                </el-table-column>
                <el-table-column prop="phoneNumber" label="联系电话" width="120px" />
                <el-table-column prop="callStatusName" label="接通状态" />
                <el-table-column prop="callDurationSec" label="通话时长">
                    <template #default="scope">
                        {{ scope.row.callDurationSec + 's' || '-' }}
                    </template>
                </el-table-column>
                <!-- <el-table-column prop="callTimes" label="拨打次数" /> -->
                <el-table-column prop="aiTagName" label="对话标签">
                    <template #default="scope">
                        {{ scope.row.aiTagName || '-' }}
                    </template>
                </el-table-column>

                <el-table-column label="操作" fixed="right" width="100" align="left">
                    <template #default="scope">
                        <a
                            v-if="scope.row.callTimes > 0"
                            @click="toRecordsDetail(scope.row)"
                            class="pointer color-blue"
                            style="text-decoration: none"
                        >
                            通话详情
                        </a>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <!-- <div class="h-120 back-color-red"></div> -->
        <el-affix position="bottom" :offset="0">
            <div class="display-flex top-bottom-center all-padding-18 justify-flex-end back-color-white">
                <el-pagination
                    v-model:currentPage="pageInfo.page"
                    v-model:page-size="pageInfo.pageSize"
                    :page-sizes="[10, 20, 30, 40, 50, 100]"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="pageInfo.total"
                    @size-change="getData"
                    @current-change="getData"
                />
            </div>
        </el-affix>
        <CallRecord v-model:visible="callRecordVisible" :task-info="currentTask" />
    </div>
</template>

<style lang="scss" scoped>
.dialing-contacts-table .el-table {
    --el-table-header-bg-color: var(--table-bg-2);
    --el-table-header-text-color: var(--main-black);
    --el-font-size-base: 16px;
}
</style>
