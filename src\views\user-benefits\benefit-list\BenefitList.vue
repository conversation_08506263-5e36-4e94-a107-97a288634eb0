<template>
    <div style="background-color: #f7f7f7; ">
        <div style=" background-color: #fff; padding: 16px">
            <!-- 搜索公共组件 -->
            <searchBox
                :searchOptionKey="searchOptionsKey"
                @updateSearchParams="updateSearchParams"
                :defaultValue="defaultQueryParams"
                :customConfig="searchConfig"
            >
            </searchBox>
        </div>
        <div class="t-margin-16" style="background-color: #fff; padding: 16px; box-sizing: border-box">
            <div class="flex flex-row b-margin-16 space-between">
                <el-dropdown placement="bottom-start">
                    <el-button :loading="exporting" :disabled="exporting">
                        导出
                        <el-icon class="l-margin-8"><CaretBottom /></el-icon>
                    </el-button>
                    <template #dropdown>
                        <el-dropdown-menu>
                            <!-- <el-dropdown-item @click="exportRecord()">导出所选</el-dropdown-item>
                            <el-dropdown-item @click="exportRecord(100)">导出前100条</el-dropdown-item>
                            <el-dropdown-item @click="exportRecord(500)">导出前500条</el-dropdown-item> -->
                        </el-dropdown-menu>
                    </template>
                </el-dropdown>
                <el-select
                    v-model="orderValue"
                    placeholder="请选择排序方式"
                    style="width: 150px"
                    @change="orderChange(orderValue)"
                >
                    <el-option
                        v-for="item in orderOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    >
                    </el-option>
                </el-select>
            </div>
            <el-table
                ref="tableList"
                :data="tableData"
                style="width: 100%;height: 500px;"
                v-loading="tableLoading"
                show-overflow-tooltip
                empty-text="暂无数据"
                :header-cell-style="{ background: '#F5F7FA', color: '#333' }"
            >
                <el-table-column label="权益名称" min-width="100" >
                    <template #default="scope">
                        {{ scope.row.service.service_name }}
                    </template>
                </el-table-column>
                <el-table-column label="总额度" prop="quantity" min-width="50"/>
                <el-table-column label="剩余额度" prop="balance" min-width="50"/>
                <el-table-column label="状态" prop="is_expired" min-width="50">
                    <template #default="scope">
                        {{ scope.row.is_expired ? '过期' : '有效' }}
                    </template>
                </el-table-column>
                <el-table-column label="购买时间" prop="created_at" min-width=“150” />
                <el-table-column label="到期时间" prop="expire_time" min-width=“150” />
                <el-table-column fixed="right" label="操作" min-width="100">
                    <template #default="scope">
                        <div class="display-flex gap-16">
                            <div
                                class="pointer"
                                style="color: #1966ff"
                                type="primary"
                                @click="allocate(scope.row)"
                            >
                                权益分配
                            </div>
                            <div
                                class="pointer"
                                style="color: #1966ff"
                                type="primary"
                                @click="openBenefitDrawer(scope.row)"
                            >
                                消费明细
                            </div>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
            <el-affix position="bottom">
                <div class="pagination-bar">
                    <el-pagination
                        v-model:currentPage="pageInfo.page"
                        v-model:page-size="pageInfo.pageSize"
                        :total="pageInfo.total"
                        layout="total, sizes, prev, pager, next, jumper"
                        @change="pageChange"
                    />
                </div>
            </el-affix>    
        </div>
    </div>
    <el-dialog
        title="权益分配"
        v-model="dialogVisible"
        width="500px"
    >
        <div class="border b-margin-16"></div>
        <div class="display-flex flex-column gap-16">
            <span class="color-two-grey font-16">已选择权益</span>
            <span class="color-black font-16">{{ currentServiceName }} (有效时间 {{ expireTime }}) </span>
            <span class="color-two-grey font-16">选择对象</span>
            <el-select 
                v-model="selectedUserId" 
                placeholder="选择分配对象"
                clearable
                size="large"
            >
                <el-option
                    v-for="item in selectedUser"
                    :key="item.id"
                    :value="item.id"
                    :label="item.name"
                >
                </el-option>
            </el-select>
            <span>设置数量</span>
            <el-input-number v-model="benefitQuantity" :min="1" :max="maxQuantity" :step="1" style="width: 122.4px">   
            </el-input-number>
        </div>
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="handleClose" style="height: 40px">取 消</el-button>
                <el-button type="primary" @click="confirmAllocate" :loading="confirmLoading" style="height: 40px">
                    确 认
                </el-button>
            </div>
        </template>
    </el-dialog>
    <div>
        <BenefitDrawer v-model:visible="benefitDrawerVisible" />
    </div>
</template>

<script lang='ts' setup>
import { ref, onMounted, computed, reactive } from 'vue'
import { useStore } from 'vuex'
import { useRoute } from 'vue-router'
import searchBox from '@/components/common/SearchBox.vue'
import type { RootState } from '@/types/store'
import orderService from '@/service/orderService'
import systemService from '@/service/systemService'
import type { IServiceOrderPageParams, IServiceOrderResponseItem } from '@/types/order'
import { ElMessage } from 'element-plus'
import type { ITenantListResponse } from '@/types/tenant'
import BenefitDrawer from '../components/BenefitDrawer.vue'

type CustomConfig = {
  [key: string]: Array<{
    label: string;
    value: string;
  }>;
}
const searchConfig = ref<CustomConfig>()
const searchOptionsKey = ref('BENEFIT_LIST_SEARCH_OPTIONS')
const route = useRoute()
const tableLoading = ref<boolean>(false)
const exporting = ref(false)
const store = useStore<RootState>()

const isPlatManager = computed(() => {
    const { account } = store.state.user
    const { user } = account || {}
    const { tenantId } = user || {}
    return tenantId === ''
})

const orderValue = ref('')
const orderOptions = ref([
    {
        value:'balanceAsc',
        label:'剩余额度-升序'
    },
    {
        value:'balanceDesc',
        label:'剩余额度-降序'
    },
    {
        value:'expireTimeAsc',
        label:'到期时间-升序'
    },
    {
        value:'expireTimeDesc',
        label:'到期时间-降序'
    },
])

const totalNum = ref(0)
const pageInfo = reactive({
    page: 1,
    pageSize: 20,
    total: totalNum,
})

const orderChange = (orderValue:string) => {
    console.log(orderValue)
}

// const exportRecord = (num?: number) => {
// let params: IAutoDialerTaskExportRequest = {}

// if (!num) {
//     if (multipleSelection.value.length === 0) {
//         return ElMessage.warning('请选择要导出的记录')
//     }
//     params.ids = multipleSelection.value.map((item) => item.id)
// } else if (num > 0) {
//     params = { ...props.filterParams, nums: num }
// }

// if (exporting.value) return

// outboundService.taskExport(params)
//     .then((res) => {
//         if(res.data.type === 'application/vnd.ms-excel'){
//             exporting.value = true
//             downloadFile(res)
//             exporting.value = false
//         }
//         else{
//             ElMessage.success('您的导出任务已创建，请到任务管理页面查看下载')
//         }
//     })
//     .catch(() => {
//         exporting.value = false
//         ElMessage.warning('导出失败，请稍后再试')
//     })
// }

const queryParams = ref<IServiceOrderPageParams>({
    page:pageInfo.page,
    pageSize:pageInfo.pageSize,
})

const tableData = ref<IServiceOrderResponseItem[]>([])

const getBenefitList = (Params: IServiceOrderPageParams) => {
    tableLoading.value = true
    Params.page = pageInfo.page
    Params.pageSize = pageInfo.pageSize
    orderService.orderServiceOrderPage(Params).then((res) => {
        console.log(res)
        if(res.success){
            pageInfo.total = res.total
            tableData.value = res.data
        }else{
            ElMessage.error('系统错误')
        }
    }).then(() => {
        tableLoading.value = false
    })
}

const pageChange = (currentPage: number, currentPagesize: number) => {
    queryParams.value.page = currentPage
    queryParams.value.pageSize = currentPagesize
    getBenefitList(queryParams.value)
}

const updateSearchParams = (params: IServiceOrderPageParams) =>{
    queryParams.value = params
    getBenefitList(queryParams.value)
}

const dialogVisible = ref(false)
const currentServiceName = ref('')
const expireTime = ref('')
const selectedUserId = ref('')
const selectedUser = ref<ITenantListResponse[]>([])
const benefitQuantity = ref(1)
const maxQuantity = ref()
const confirmLoading = ref(false)
const allocate = (service: IServiceOrderResponseItem) => {
    dialogVisible.value = true
    console.log(service)
    currentServiceName.value = service.service.service_name
    expireTime.value = service.expire_time
    maxQuantity.value = service.balance
}

const handleClose = () => {
    dialogVisible.value = false
}

const confirmAllocate = () => {
    confirmLoading.value = true
}
const benefitDrawerVisible = ref(false)
const openBenefitDrawer = (service: IServiceOrderResponseItem) => {
    console.log(service)
    benefitDrawerVisible.value = true
}

let defaultQueryParams = reactive<Record<string, boolean | string | number[] | string[]>>({})
onMounted(() => {
    // 可分配的租户
    systemService.tenantList().then(response => {
        console.log(response)
        selectedUser.value = response
    })

    if(isPlatManager.value){
        searchOptionsKey.value = 'BENEFIT_LIST_SEARCH_OPTIONS_FORADMIN'
        systemService.tenantList().then(response => {
            searchConfig.value = {
                ...searchConfig.value,
                tenantId:response.map(item => ({
                    label:item.name,
                    value:item.id
                }))
            }
        })
    }
    if (route.query && JSON.stringify(route.query) !== '{}') {
        console.log('route.query', route.query)
        for (const key in route.query) {
            defaultQueryParams = {
                ...defaultQueryParams,
                [key]: route.query[key] as boolean | string | number[] | string[],
            }
        }
    } else {
        getBenefitList(queryParams.value)
    }
})

</script>

<style lang='scss' scoped>
@use '@/styles/element-lead.scss';

.pagination-bar {
    display: flex;
    flex-direction: row-reverse;
    width: 100%;
    background-color: #fff;
    padding-top: 16px;
    padding-bottom: 16px;
}
</style>