<template>
    <div style="background-color: #f7f7f7;">
        <div style=" background-color: #fff; padding: 16px">
            <div class="t-margin-10 display-flex" style="height: 40px">
                <el-input placeholder="请输入内容" v-model="input" style="width: 30%">
                    <template #prefix>
                        <Icon icon="icon-a-chazhaoqiye"></Icon>
                    </template>
                </el-input>
                <div class="l-margin-10 search-btn pointer font-16" @click="search">搜 索</div>
                <div class="display-flex top-bottom-center" v-if="activeName === 'searchproject'">
                    <el-checkbox v-model="matchtitle" label="匹配项目的标题" class="l-margin-20"></el-checkbox>
                    <el-checkbox v-model="matchSubjectMatter" label="匹配标的物"></el-checkbox>
                </div>
                <div class="display-flex top-bottom-center" v-if="activeName === 'searchcompany'">
                    <el-checkbox v-model="matchcompany" label="匹配企业名称" class="l-margin-20" disabled></el-checkbox>
                </div>
            </div>
            <div class="t-margin-20">
                <el-tabs v-model="activeName" @tab-change="handleTabChange()" class="b-margin-20">
                    <el-tab-pane v-if="isShowTender" label="招标投标项目查询" name="searchproject">
                        <BidSearchFilter />
                    </el-tab-pane>
                    <el-tab-pane label="企业查询" name="searchcompany">
                        <ProjectSearchFilter />
                    </el-tab-pane>
                </el-tabs>
            </div>
        </div>
        <div class="t-margin-16" style="background-color: #fff; padding: 16px; box-sizing: border-box">
            <div class="display-flex t-margin-4 top-bottom-center">
                <div class="r-margin-10 font-navbar">
                    共
                    <span class="color-blue lr-padding-2 font-weight-600">{{
                        totalnum > 1000000 ? '100w+' : totalnum
                    }}</span>
                    个结果
                </div>
                <div v-if="activeName === 'searchcompany'" class="color-border">|</div>
                <div v-if="activeName === 'searchcompany'" class="l-margin-10 font-navbar">
                    已选
                    <span class="color-blue lr-padding-2 font-weight-600">
                        {{ selectedLength }}
                    </span>
                    个
                </div>
                <el-dropdown v-if="activeName === 'searchcompany'" class="l-margin-10">
                    <el-button>
                        转CRM
                        <el-icon class="el-icon--right">
                            <CaretBottom />
                        </el-icon>
                    </el-button>
                    <template #dropdown>
                        <el-dropdown-menu>
                            <el-dropdown-item @click="batchTransfer()">转移所选</el-dropdown-item>
                        </el-dropdown-menu>
                    </template>
                </el-dropdown>
            </div>
            <div class="t-margin-10" v-loading="tableLoading" element-loading-text="加载中...">
                <SearchTenderandbidProject
                    :tableData="projectTableData"
                    @selection-change="selectionChange"
                    v-if="activeName === 'searchproject'"
                />
                <SearchTenderandbidCompany
                    tableRef="companyTableRef"
                    :tableData="companyTableData"
                    :refreshCompanyList="refreshCompanyList"
                    @selection-change="selectionChange"
                    v-if="activeName === 'searchcompany'"
                />
                <el-affix position="bottom" :offset="0">
                    <div class="pagination-bar">
                        <el-pagination
                            v-model:currentPage="pageInfo.page"
                            v-model:page-size="pageInfo.pageSize"
                            :total="pageInfo.total"
                            layout="total, sizes, prev, pager, next, jumper"
                            @change="pageChange"
                        />
                    </div>
                </el-affix>
            </div>
        </div>
        <TransferCrmDialog
            v-model:visible="transferCrmDialogVisible"
            :selected="multipleSelection"
            :success="transferCrmSuccess"
        />
    </div>
</template>

<script lang="ts" setup>
import type {
    ISearchBidProjectItem,
    ISearchBidCompanyItem,
    ISearchBidAndFactoryParams,
    FilterResultValue,
    ICompanyInfo,
} from '@/types/company'
import Icon from '@/components/common/Icon.vue'
import { ref, computed, reactive, onMounted, watch } from 'vue'
import {
    BidSearchFilter,
    ProjectSearchFilter,
    SearchTenderandbidCompany,
    SearchTenderandbidProject,
} from './components'
import { useStore } from 'vuex'
import type { RootState } from '@/types/store'
import { formatBidFilters } from '@/utils/enterprise/filters'
import aicService from '@/service/aicService'
import TransferCrmDialog from '@/components/transfer-crm-dialog/TransferCrmDialog.vue'
import systemService from '@/service/systemService'

const isShowTender = ref<boolean>(true)
const getDataSource = () => {
    if (user.value?.tenantId) {
        systemService.tenantGetAicChannel({tenantId: user.value!.tenantId}).then((response) => {
            console.log('dataSource', response)
            if(response.channelType === '1'){
                isShowTender.value = true
                activeName.value = 'searchproject'
                search()
            }else{
                isShowTender.value = false
                activeName.value = 'searchcompany'
                search()
            }
        }).catch((error) => {
            console.log('error', error)
        })
    }else{
        isShowTender.value = true
        activeName.value = 'searchproject'
        search()
    }
    
}

const companyTableRef = ref()
const multipleSelection = ref<ICompanyInfo[]>([])
const transferCrmDialogVisible = ref(false)
const store = useStore<RootState>()
const user = computed(() => {
    const { account } = store.state.user || {}
    const { user } = account || {}
    return user
})
const tableLoading = ref(false)
const input = ref('')
const matchtitle = ref(true)
const matchSubjectMatter = ref(true)
const matchcompany = ref(true)
const activeName = ref('')
const selectedData = ref<ICompanyInfo[]>([])
const projectTableData = ref<ISearchBidProjectItem[]>([])
const companyTableData = ref<ISearchBidCompanyItem[]>([])
const companyTabletotal = ref(0)
const totalnum = computed(() => companyTabletotal.value)
const selectedLength = computed(() => selectedData.value.length)
const bidSearchParams = computed(() => {
    const { tenderFilterParams } = store.state.enterprise
    return tenderFilterParams
})
const bidCompanySearchParams = computed(() => {
    const { projectFilterParams } = store.state.enterprise
    return projectFilterParams
})
const bidFilterParams = ref<{ [x: string]: FilterResultValue }>({})
const bidCompanyFilterParams = ref<{ [x: string]: FilterResultValue }>({})


const pageInfo = reactive({
    page: 1,
    pageSize: 20,
    total: totalnum,
})

const requestProjectParams = ref<ISearchBidAndFactoryParams>({
    belongsArea: {
        province: [],
        city: [],
        district: [],
    },
    keyword: '',
    matchField: ['title', 'subjectMatter'],
    matchType: '',
    model: '',
    page: pageInfo.page,
    pageSize: pageInfo.pageSize,
    scope: '',
    sortBy: 0,
    tenderBusinessType: [],
    tenderProjectType: [],
    tenderPublishTime: [],
})

const requestCompanyParams = ref<ISearchBidAndFactoryParams>({
    area: {
        province: [],
        city: [],
        district: [],
    },
    contactType_hasMore: [],
    esDate_customer: [],
    keyword: '',
    matchType: '',
    model: '',
    page: pageInfo.page,
    pageSize: pageInfo.pageSize,
    regCapUnify_customer: [],
    scope: '',
    sortBy: 0,
    tenderCompanyRole: [],
})
const pageChange = (currentPage: number, currentPagesize: number) => {
    requestProjectParams.value.page = currentPage
    requestProjectParams.value.pageSize = currentPagesize
    requestCompanyParams.value.page = currentPage
    requestCompanyParams.value.pageSize = currentPagesize
    search()
}

// 定义tab点击事件处理函数
const handleTabChange = () => {
    input.value = ''
    if (pageInfo.page !== 1) {
        pageInfo.page = 1
    }
    search()
}

const getProjectInfo = async (params: ISearchBidAndFactoryParams) => {
    tableLoading.value = true
    params.page = pageInfo.page
    params.pageSize = pageInfo.pageSize
    aicService
        .searchTenderProject({ ...params, ...bidFilterParams.value })
        .then((response) => {
            tableLoading.value = false
            companyTabletotal.value = response.total
            projectTableData.value = response.data
            console.log('查询结果', response)
        })
        .catch((error) => {
            const { isCanceled } = error
            if (!isCanceled) {
                tableLoading.value = false
                companyTabletotal.value = 0
                projectTableData.value = []
            }
        })
}

const refreshCompanyList = () => {
    search()
}
const getCompanyInfo = async (params: ISearchBidAndFactoryParams) => {
    tableLoading.value = true
    params.page = pageInfo.page
    params.pageSize = pageInfo.pageSize
    aicService
        .searchScene({ ...params, ...bidCompanyFilterParams.value })
        .then((response) => {
            tableLoading.value = false
            const { errCode } = response || {}
            if (errCode === 0) {
                companyTabletotal.value = response.total
                companyTableData.value = response.data
                console.log('查询结果', response)
            } else {
                companyTabletotal.value = 0
                companyTableData.value = []
            }
        })
        .catch((error) => {
            const { isCanceled } = error
            if (!isCanceled) {
                tableLoading.value = false

                companyTabletotal.value = 0
                companyTableData.value = []
            }
        })
        .finally(() => {
            tableLoading.value = false
        })
}

const search = () => {
    if (activeName.value === 'searchproject') {
        requestProjectParams.value.page = pageInfo.page
        requestProjectParams.value.pageSize = pageInfo.pageSize
        requestProjectParams.value.keyword = input.value
        requestProjectParams.value.matchField = []
        if (matchtitle.value === true) {
            requestProjectParams.value.matchField.push('title')
        }
        if (matchSubjectMatter.value === true) {
            requestProjectParams.value.matchField.push('subjectMatter')
        }
        getProjectInfo({ ...requestProjectParams.value, ...bidFilterParams.value })
    } else if (activeName.value === 'searchcompany') {
        requestCompanyParams.value.page = pageInfo.page
        requestCompanyParams.value.pageSize = pageInfo.pageSize
        requestCompanyParams.value.keyword = input.value

        getCompanyInfo({ ...requestCompanyParams.value, ...bidCompanyFilterParams.value })
    }
}

const batchTransfer = () => {
    transferCrmDialogVisible.value = true
    console.log('transferCrmDialogVisible', transferCrmDialogVisible.value)
}

// 选择
const selectionChange = (val: ICompanyInfo[]) => {
    selectedData.value = val
    multipleSelection.value = val
    // console.log('selectedData', selectedData.value.length)
}


const transferCrmSuccess = () => {
    if (companyTableRef.value) {
        companyTableRef.value.clearSelection()
    }
}

watch(
    () => bidSearchParams,
    (value) => {
        if (activeName.value === 'searchproject') {
            const formattedData = formatBidFilters(value.value)
            bidFilterParams.value = formattedData
            requestProjectParams.value.keyword = input.value
            getProjectInfo({ ...requestProjectParams.value })
        }
    },
    { deep: true }
)

watch(
    () => bidCompanySearchParams,
    (value) => {
        if (activeName.value === 'searchcompany') {
            const formattedData = formatBidFilters(value.value)
            bidCompanyFilterParams.value = formattedData
            requestProjectParams.value.keyword = input.value
            getCompanyInfo({ ...requestCompanyParams.value })
        }
    },
    { deep: true }
)

onMounted(async () => {
    console.log('123123213123123', user.value?.tenantId)
    getDataSource()
})
</script>

<style lang="scss" scoped>
@use '@/styles/element-lead.scss';
.search-btn {
    background: #1966ff;
    border-radius: 4px;
    color: #fff;
    width: 68px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.justify-center {
    justify-content: center;
}
.pagination-bar {
    display: flex;
    flex-direction: row-reverse;
    width: 100%;
    background-color: #fff;
    padding-top: 16px;
    padding-bottom: 16px;
}
</style>
