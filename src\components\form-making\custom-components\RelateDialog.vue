<template>
    <el-dialog append-to-body v-model="relateCompanyVisible" :title="`【${relateInfo.legalperson}】关联企业数据`"
               @close="closeDialog">

        <el-table v-loading="loading" ref="table" :data="companyRelateModelData" tooltip-effect="dark" border
                  table-layout="fixed" :header-cell-style="{
                      background: '#ECF5FF',
                  }" size="large" empty-text="暂无数据">
            <el-table-column type="index" label="序号" width="80" align="center" :index="indexFilter"
                             header-align="center"></el-table-column>
            <el-table-column label="企业名称" prop='entName' align="center" header-align="center">
                <template #default="scope">
                    <div class="pointer color-blue" @click="showCompanyDetail(scope.row)">
                        {{ scope.row.entName }}
                    </div>

                </template>
            </el-table-column>
            <el-table-column label="职位" prop='position' align="center" header-align="center"></el-table-column>
            <el-table-column label="任职状态" prop='positionStatus' align="center" header-align="center"></el-table-column>
            <el-table-column label="注册资本" prop='regCapital' align="center" header-align="center"></el-table-column>
            <el-table-column label="地区" prop='address' align="center" header-align="center"></el-table-column>
            <el-table-column label="持股比例" prop='shareholdingRatio' align="center"
                             header-align="center"></el-table-column>
        </el-table>
        <div class="display-flex top-bottom-center margin-top-20 justify-flex-end">
            <el-pagination :hide-on-single-page="true" v-model:currentPage="relationPageInfo.page"
                           v-model:page-size="relationPageInfo.pageSize" layout="total, prev, pager, next"
                           :total="relationPageInfo.total" @current-change="gsGetPersonEnterpriseRelations()" />
        </div>
    </el-dialog>
</template>

<script lang='ts' setup>
import { ref, onMounted, defineProps, defineEmits, watch, inject } from 'vue'
import type { Ref } from 'vue'
import { useRouter } from 'vue-router'
import type { CompanyBaseInfo, RelateCompany } from '@/types/company'
import type { PersonEnterpriseRelationsItem } from '@/types/aic'

import { ElMessage } from 'element-plus'
import aicService from '@/service/aicService'


const router = useRouter()

const emits = defineEmits(['update:dialogVisable'])

const props = defineProps({
    dialogVisable: {
        type: Boolean,
        default: false
    },
    relateInfo: {
        type: Object,
        default: () => { }
    }

})

const indexFilter = (index: number) => {
    // (当前页-1)*每页显示的条数+table索引+1
    return (relationPageInfo.value.page - 1) * relationPageInfo.value.pageSize + index + 1
}


const relateCompanyVisible: Ref<boolean> = ref(props.dialogVisable)

const loading: Ref<boolean> = ref(false)

const companyInfo: Ref<CompanyBaseInfo> = inject('companyInfo') as Ref<CompanyBaseInfo>

const relationPageInfo: Ref<{ page: number, pageSize: number, total: number }> = ref({
    page: 1,
    pageSize: 10,
    total: 0,
})

const showCompanyDetail = (item: RelateCompany) => {
    aicService.searchEnterprise({
        keyword: item.entName,
        scope: 'companyname',
        pageSize: 1,
        page: 1
    }).then((res) => {
        console.log(res)
        let company = res.data[0]
        if (company.companyName === item.entName) {
            const routeUrl = router.resolve({ name: 'company-profile', params: { socialCreditCode: company.socialCreditCode } }).href
            window.open(routeUrl, '_blank')
        } else {
            ElMessage({
                message: '未找到该公司',
                type: 'error',
            })
        }


    }).finally(() => { })
}

const companyRelateModelData: Ref<PersonEnterpriseRelationsItem[]> = ref([])
const gsGetPersonEnterpriseRelations = () => {
    console.log('props', props.relateInfo)

    let entId = props.relateInfo?.pid || companyInfo.value?.id
    let relateName = props.relateInfo?.legalperson
    let data = {
        entId: entId,
        name: relateName,
        companyName: companyInfo.value?.companyName || '',
        socialCreditCode: '',
        page: relationPageInfo.value.page
    }
    let convertToPercentage = (str: string) => {
        let num = parseFloat(str)
        if (isNaN(num)) {
            return '-'
        }
        return (num).toFixed(2) + '%'
    }
    loading.value = true
    aicService.gsGetPersonEnterpriseRelations(data).then((res) => {
        console.log('----', res)
        let list = res.data.items.map(item => {
            return { ...item, shareholdingRatio: convertToPercentage(item.shareholdingRatio) }
        })
        console.log(list)
        companyRelateModelData.value = list
        relationPageInfo.value.total = res.data.total
    }).finally(() => {
        loading.value = false
    })
}


const closeDialog = () => {
    relateCompanyVisible.value = false
    emits('update:dialogVisable', false)
}

watch(() => props.dialogVisable, (nVal) => {
    relateCompanyVisible.value = nVal
})

onMounted(() => {
    gsGetPersonEnterpriseRelations()
})

</script>

<style lang='scss' scoped></style>