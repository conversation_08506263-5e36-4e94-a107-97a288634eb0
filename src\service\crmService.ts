import http from '@/axios'

import type {
    IGetCrmLeadParams,
    IGetLeadResponse,
    IGetTabPool,
    IPageInfo,
    IGetTagResponse,
    IDelLeadResponse,
    IDelLeadParams,
    ILeadTransfelParams,
    ICrmAddParams,
    ICrmGetActiviesParams,
    ICrmGetActiviesResponse,
    ISearchPoolListParams,
    SearchPoolListResponse,
    IAddPoolParams,
    IAddAndDeleteResponse,
    ILeadData,
    SetRuleParams,
    IUpdateContactsResponse,
    IUpdateContactsRequest,
    IUpdateActiviesParams,
    ICrmAddFromListRequest,
    FileUploadResponse,
    ICrmSendSmsParams,
    ICrmUpdateParams,
    ICrmExportParams,
    ICrmImportParams,
    SearchPoolListResponseItem,
    IAddTagParams,
    CrmGoodsPolicyListParams,
    CrmGoodsPolicyListResponse,
    IUpdateTagParams,
    ITagCountResponse,
    IGoodsProductItem,
    IRuleUpdateParams,
    CrmGoodsPolicyEnumDataResponse,
    IGoodsPolicyItem,
    CrmGoodsFinanceListParams,
    CrmGoodsFinanceEnumDataResponse,
    CrmGoodsFinnanceListResponse,
    IGoodsFinanceMatchDetailParams,
    IGoodsPolicyTransferReauest,
    ITagAddResponse,
    ICrmImportBatchEnumParams,
    ICrmImportBatchEnumResponse,
    GsGetCompanyClueInfoResponse,
} from '@/types/lead'
import type { ICommonResponse } from '@/types/axios'
import type {
    IServiceDetailResponse,
    IServiceEditRequest,
    IServiceEditResponse,
    IServiceSwitchRequest
} from '@/types/service'
export default {
    crmList(data: IGetCrmLeadParams): Promise<IGetLeadResponse> {
        return http.get(`/api/zhenqi-crm/crm/list`, {
            params: data,
            hideError: true,
        })
    },
    crmTabPool(): Promise<IGetTabPool[]> {
        return http.get(`/api/zhenqi-crm/crm/tab-pool`)
    },
    crmTagList(data: IPageInfo): Promise<IGetTagResponse> {
        return http.get(`/api/zhenqi-crm/crm/tag-list`, {
            params: data,
            hideError: true,
        })
    },
    crmDelete(data: IDelLeadParams): Promise<IDelLeadResponse> {
        return http.delete(`/api/zhenqi-crm/crm/delete`, {
            params: data,
            hideError: true,
        })
    },
    // 转移线索
    crmTransfer(data: ILeadTransfelParams): Promise<IDelLeadResponse> {
        return http.post(`/api/zhenqi-crm/crm/transfer`, data)
    },
    //线索详情
    crmDetail(body: { id: string }): Promise<ILeadData> {
        return http.get(`/api/zhenqi-crm/crm/detail`, {
            params: body,
        })
    },
    // 获取跟进记录
    crmGetActivities(data: ICrmGetActiviesParams): Promise<ICrmGetActiviesResponse> {
        return http.get(`/api/zhenqi-crm/crm/get-activies`, {
            params: data,
            hideError: true,
        })
    },
    // 新增跟进记录
    crmAddActivies(data: IUpdateActiviesParams): Promise<ICommonResponse> {
        return http.post(`/api/zhenqi-crm/crm/add-activies`, data, {
            hideError: true,
        })
    },

    crmManageList(data: ISearchPoolListParams): Promise<SearchPoolListResponse> {
        return http.get(`/api/zhenqi-crm/crm/manage/list`, {
            hideError: true,
            params: data,
        })
    },

    crmManageAdd(data: IAddPoolParams): Promise<IAddAndDeleteResponse> {
        return http.post('/api/zhenqi-crm/crm/manage/add', data, {
            hideError: true,
        })
    },

    // 线索池详情
    crmManageDetail(body: { poolId: string }): Promise<SearchPoolListResponseItem> {
        return http.get(`/api/zhenqi-crm/crm/manage/detail`, {
            params: body,
        })
    },

    crmManageUpdate(data: IAddPoolParams | SetRuleParams): Promise<IAddAndDeleteResponse> {
        return http.post('/api/zhenqi-crm/crm/manage/update', data, {
            hideError: true,
        })
    },

    crmManageDelete(id: string): Promise<IAddAndDeleteResponse> {
        return http.delete(`/api/zhenqi-crm/crm/manage/delete?`, {
            params: { id },
            hideError: true,
        })
    },
    // 新增接口
    crmAdd(data: ICrmAddParams) {
        return http.post(`/api/zhenqi-crm/crm/add`, data)
    },
    // 更新接口
    crmUpdate(body: ICrmUpdateParams) {
        return http.put(`/api/zhenqi-crm/crm/update`, body)
    },
    // 更新联系方式接口
    crmUpdateContacts(body: IUpdateContactsRequest[]): Promise<IUpdateContactsResponse> {
        return http.put(`/api/zhenqi-crm/crm/update-contacts`, body, {
            hideError: true,
        })
    },
    crmAddFromList(data: ICrmAddFromListRequest): Promise<IDelLeadResponse> {
        return http.post(`/api/zhenqi-crm/crm/add-from-list`, data, { hideError: true })
    },
    crmFileUpload(): Promise<FileUploadResponse> {
        return http.post(`/api/zhenqi-crm/file/upload`, {
            hideError: true,
        })
    },
    // 导出接口
    crmExport(data: ICrmExportParams): Promise<Blob> {
        return http.post(`/api/zhenqi-crm/crm/export`, data, {
            responseType: 'blob',
            hideError: true,
        })
    },
    // 导入接口
    crmImport(data: ICrmImportParams) {
        const { type } = data
        return http.post(`/api/zhenqi-crm/crm/import/${type}`, data)
    },
    // 获取批次下拉数据
    crmImportBatchEnum(data: ICrmImportBatchEnumParams): Promise<ICrmImportBatchEnumResponse> {
        return http.get('/api/zhenqi-crm/crm/import-batch-enum', {
            params: data,
            hideError: true
        })
    },
    // 发送短信
    crmSendSms(data: ICrmSendSmsParams): Promise<IDelLeadResponse> {
        return http.post(`/api/zhenqi-crm/crm/send-sms`, data, { hideError: true })
    },
    crmUpdateStatus(body: { leadId: string; status: number }): Promise<IDelLeadResponse> {
        return http.put(`/api/zhenqi-crm/crm/update-status`, body)
    },
    // 新增标签
    crmTagAdd(data: IAddTagParams): Promise<ITagAddResponse> {
        return http.post('/api/zhenqi-crm/crm/tag-add', data, { hideError: true })
    },
    // 删除标签
    crmTagDelete(id: string): Promise<ICommonResponse> {
        return http.delete(`api/zhenqi-crm/crm/tag-delete?id=${id}`, { hideError: true })
    },
    // 编辑标签
    crmTagUpdate(data: IUpdateTagParams): Promise<ICommonResponse> {
        return http.post('/api/zhenqi-crm/crm/tag-update', data, { hideError: true })
    },
    // 标签绑定计数
    crmTagCount(data: { tagId: string }): Promise<ITagCountResponse> {
        return http.get('/api/zhenqi-crm/crm/tag-count',
            {
                params: data,
                hideError: true
            })
    },
    crmGoodsPolicyList(body: CrmGoodsPolicyListParams): Promise<CrmGoodsPolicyListResponse> {
        return http.get('/api/zhenqi-crm/goods/policy/list', {
            params: body,
            hideError: true,
        })
    },
    // 业务配置详情
    serviceDetail(body: { type: number }): Promise<IServiceDetailResponse> {
        return http.get(`/api/zhenqi-crm/service/detail`, {
            params: body,
        })
    },
    // 业务配置详情
    serviceSwitch(body: IServiceSwitchRequest): Promise<IServiceDetailResponse> {
        return http.post(`/api/zhenqi-crm/service/switch`, body)
    },
    serviceUpdate(body: IServiceEditRequest): Promise<IServiceEditResponse> {
        return http.post(`/api/zhenqi-crm/service/update`, body, {
            hideError: true,
        })
    },
    // 企业匹配规则
    goodsFinanceEntMatchRule(body: { companyId: string }): Promise<IGoodsProductItem[]> {
        return http.get(`/api/zhenqi-crm/goods/finance/ent-match-rule`, {
            params: body
        })
    },
    crmGoodsPolicyRuleUpdate(data: IRuleUpdateParams) {
        return http.post('/api/zhenqi-crm/goods/policy/rule-update', data)
    },
    crmGoodsPolicyEnumData(): Promise<CrmGoodsPolicyEnumDataResponse> {
        return http.get('/api/zhenqi-crm/goods/policy/enum-data', {
            params: {}
        })
    },
    crmGoodsPolicyAdd(data: IGoodsPolicyItem) {
        return http.post('/api/zhenqi-crm/goods/policy/add', data)
    },
    crmGoodsPolicyUpdate(data: IGoodsPolicyItem) {
        return http.post('/api/zhenqi-crm/goods/policy/update', data)
    },
    crmGoodsPolicyDelete(id: string) {
        return http.delete('/api/zhenqi-crm/goods/policy/delete', {
            params: { id }
        })
    },
    crmGoodsPolicyRuleMatchEnt(id: string): Promise<IGoodsPolicyItem[]> {
        return http.get('/api/zhenqi-crm/goods/policy/rule-match-ent', {
            params: { id }
        })
    },
    crmGoodsFinanceRuleMatchEnt(id: string): Promise<IGoodsProductItem[]> {
        return http.get('/api/zhenqi-crm/goods/finance/rule-match-ent', {
            params: { id }
        })
    },
    crmGoodsPolicyEntMatchRule(body: { companyId: string }): Promise<IGoodsPolicyItem[]> {
        return http.get('/api/zhenqi-crm/goods/policy/ent-match-rule', {
            params: body
        })
    },
    crmGoodsFinanceList(body: CrmGoodsFinanceListParams): Promise<CrmGoodsFinnanceListResponse> {
        return http.get('/api/zhenqi-crm/goods/finance/list', {
            params: body,
            hideError: true,
        })
    },
    crmGoodsFinanceEnumData(): Promise<CrmGoodsFinanceEnumDataResponse> {
        return http.get('/api/zhenqi-crm/goods/finance/enum-data', {
            params: {}
        })
    },
    crmGoodsFinanceAdd(data: IGoodsProductItem) {
        return http.post('/api/zhenqi-crm/goods/finance/add', data)
    },
    crmGoodsFinanceUpdate(data: IGoodsProductItem) {
        return http.post('/api/zhenqi-crm/goods/finance/update', data)
    },
    crmGoodsFinanceDelete(id: string) {
        return http.delete('/api/zhenqi-crm/goods/finance/delete', {
            params: { id }
        })
    },
    // 查看匹配详情
    goodsFinanceMatchDetail(body: IGoodsFinanceMatchDetailParams) {
        return http.get(`/api/zhenqi-crm/goods/finance/match-detail`, {
            params: body,
        })
    },
    // 政策/金融产品转移给服务商
    goodsPolicyTransfer(body: IGoodsPolicyTransferReauest) {
        return http.post(`/api/zhenqi-crm/goods/policy/transfer`, body)
    },
    gsGetCompanyClueInfo(data: { socialCreditCode: string }): Promise<{ data: GsGetCompanyClueInfoResponse }> {
        return http.get(`/api/zhenqi-crm/crm/get-company-clue-info`, {
            params: data,
            hideError: true,
        })
    },


}
