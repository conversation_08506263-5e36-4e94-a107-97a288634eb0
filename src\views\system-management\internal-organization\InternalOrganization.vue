<script setup lang="ts">
import type { TabsPaneContext } from 'element-plus'
import { ref } from 'vue'
import { Organization, RoleMenus, RoleDataScope } from './components'
const scopeRef = ref('org')

const handleClick = (tab: TabsPaneContext, event: Event) => {
    console.log(tab, event)
}
</script>

<template>
    <div class="flex flex-column gap-16 flex-1 internal-organization">
        <div class="flex flex-column back-color-white all-padding-16 gap-20 border-radius-4 flex-1">
            <el-tabs v-model="scopeRef" @tab-click="handleClick" class="flex-1 flex">
                <el-tab-pane label="架构及用户设置" name="org" class="height-100">
                    <Organization v-if="scopeRef === 'org'"/>
                </el-tab-pane>
                <el-tab-pane label="角色权限" name="roleMenus" class="height-100">
                    <RoleMenus v-if="scopeRef === 'roleMenus'"/>
                </el-tab-pane>
                <el-tab-pane label="数据权限" name="roleDataScope" class="height-100">
                    <RoleDataScope v-if="scopeRef === 'roleDataScope'"/>
                </el-tab-pane>
            </el-tabs>
            <SearchFilter />
        </div>
    </div>
</template>

<style lang="scss" scoped>
.internal-organization :deep(.el-tabs__header) {
    margin: 0;
}
</style>
