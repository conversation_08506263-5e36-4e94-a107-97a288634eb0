<script lang="ts" setup>
import type { IAutoDialerTaskCompanyFilter, IAutoDialerTaskDetailInfoItem } from '@/types/autoDialer'
import { ref } from 'vue'
import { useRoute } from 'vue-router'
import CompanyListFilter from './components/CompanyListFilter.vue'
import CompanyListTable from './components/CompanyListTable.vue'

// ====================== Imports ======================

// ====================== Interfaces & Types ======================

// ====================== Props & Emits ======================
defineProps<{
    taskDetail: IAutoDialerTaskDetailInfoItem | null
}>()

// ====================== Store & Computed Values ======================

// ====================== Refs & Reactive State ======================
const filterParams = ref<IAutoDialerTaskCompanyFilter>()
const route = useRoute()
const taskCode = route.query.taskCode

// ====================== Methods ======================

const filterQuery = (params: IAutoDialerTaskCompanyFilter) => {
    filterParams.value = params
}

// ====================== Watchers ======================

// ====================== Lifecycle Hooks ======================
</script>

<template>
    <div class="flex flex-column gap-16 height-100 t-padding-14">
        <div class="flex flex-row gap-24 font-14">
            <div><span class="color-three-grey">企业总数：</span>{{ taskDetail?.companyNum || '-' }}</div>
            <div><span class="color-three-grey">企业成功数：</span>{{ taskDetail?.companySuccessCount || '-' }}</div>
            <div><span class="color-three-grey">企业失败数：</span>{{ taskDetail?.companyFailCount || '-' }}</div>
            <div><span class="color-three-grey">企业呼叫总数：</span>{{ taskDetail?.companyTotalCount || '-' }}</div>
        </div>
        <div class="back-color-white border-radius-4">
            <CompanyListFilter :get-data="filterQuery" />
        </div>
        <div class="back-color-white height-100 oh">
            <CompanyListTable :filter-params="filterParams" :task-code="taskCode?.toString() || ''" />
        </div>
    </div>
</template>

<style lang="scss" scoped></style>
