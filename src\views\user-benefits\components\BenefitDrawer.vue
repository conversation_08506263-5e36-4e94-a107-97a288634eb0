<template>
    <el-drawer 
        v-model="drawerVisible" 
        size="70%"
    >
        <template #header>
            <span class="font-20 color-black">
                消费明细
            </span>
        </template>
        <div class="border b-margin-16"></div>
        <div class="display-flex flex-column gap-16">
            <searchBox 
                searchOptionKey="BENEFIT_RECORD_SEARCH_OPTIONS"
                @updateSearchParams="updateSearchParams"
            />
            <div class="flex flex-row b-margin-16">
                <el-dropdown placement="bottom-start">
                    <el-button :loading="exporting" :disabled="exporting">
                        导出
                        <el-icon class="l-margin-8"><CaretBottom /></el-icon>
                    </el-button>
                    <template #dropdown>
                        <el-dropdown-menu>
                            <!-- <el-dropdown-item @click="exportRecord()">导出所选</el-dropdown-item>
                            <el-dropdown-item @click="exportRecord(100)">导出前100条</el-dropdown-item>
                            <el-dropdown-item @click="exportRecord(500)">导出前500条</el-dropdown-item> -->
                        </el-dropdown-menu>
                    </template>
                </el-dropdown>
            </div>
            <el-table
                ref="tableList"
                :data="tableData"
                style="width: 100%;min-height: 40%;"
                v-loading="tableLoading"
                show-overflow-tooltip
                empty-text="暂无数据"
                :header-cell-style="{ background: '#F5F7FA', color: '#333' }"
            >
                <el-table-column label="权益名称" min-width="100" >
                    <template #default="scope">
                        {{ scope.row.service.service_name }}
                    </template>
                </el-table-column>
                <el-table-column label="企业名称" prop="quantity" min-width="50" >
                    <template #default="scope">
                        {{ scope.row.extra_info ? JSON.stringify(scope.row.extra_info.companyName) : '-' }}
                    </template>
                </el-table-column>
                <el-table-column label="企业税号" prop="option_id" min-width="60" >
                    <template #default="scope">
                        {{ scope.row.extra_info ? scope.row.option_id : '-' }}
                    </template>
                </el-table-column>
                <el-table-column label="类型" prop="change_type" min-width="50">
                    <template #default="scope">
                        <span v-if="scope.row.change_type === 'prerevoke'">回退</span>
                        <span v-else-if="scope.row.change_type === 'deduct'">消费</span>
                        <span v-else-if="scope.row.change_type === 'prededuct'">预扣减</span>
                        <span v-else>其他</span>
                    </template>
                </el-table-column>
                <el-table-column label="数量" prop="quantity" min-width=“50” />
                <el-table-column label="操作员" prop="user_id" min-width=“100” />
                <el-table-column label="消费时间" prop="created_at" min-width=“150” />
            </el-table>            
            <el-affix position="bottom">
                <div class="pagination-bar">
                    <el-pagination
                        v-model:currentPage="pageInfo.page"
                        v-model:page-size="pageInfo.pageSize"
                        :total="pageInfo.total"
                        layout="total, sizes, prev, pager, next, jumper"
                        @change="pageChange"
                    />
                </div>
            </el-affix>   
        </div>
    </el-drawer>
</template>

<script lang='ts' setup>
import { ref, reactive, computed, onMounted } from 'vue'
import searchBox from '@/components/common/SearchBox.vue'
import type { IOrderUsageRecordParams, IOrderUsageRecordResponseItem } from '@/types/order'
import orderService from '@/service/orderService'
import { ElMessage } from 'element-plus'

const exporting = ref(false)
const props = defineProps<{
    visible: boolean
}>()

const emit = defineEmits(['update:visible'])
const drawerVisible = computed({
    get: () => props.visible,
    set: (val) => emit('update:visible', val)
})

const totalNum = ref(0)
const pageInfo = reactive({
    page: 1,
    pageSize: 20,
    total: totalNum,
})
const tableLoading = ref<boolean>(false)
const tableData = ref<IOrderUsageRecordResponseItem[]>([])
const getBenefitRecord = (Params: IOrderUsageRecordParams) => {
    tableLoading.value = true
    Params.page = pageInfo.page
    Params.pageSize = pageInfo.pageSize
    orderService.orderServiceUsagePage(Params).then((res) => {
        console.log(res)
        if(res.success){
            pageInfo.total = res.total
            tableData.value = res.data
        }else{
            ElMessage.error('系统错误')
        }
    }).then(() => {
        tableLoading.value = false
    })
}

const pageChange = (currentPage: number, currentPagesize: number) => {
    queryParams.value.page = currentPage
    queryParams.value.pageSize = currentPagesize
    getBenefitRecord(queryParams.value)
}

const updateSearchParams = (params: IOrderUsageRecordParams) =>{
    queryParams.value = params
    queryParams.value.serviceOrderId = '880'
    getBenefitRecord(queryParams.value)
}

const queryParams = ref<IOrderUsageRecordParams>({
    page:pageInfo.page,
    pageSize:pageInfo.pageSize,
    serviceOrderId:'880'
})

onMounted(() => {
    getBenefitRecord(queryParams.value)
})
</script>

<style lang='scss' scoped>
@use '@/styles/element-lead.scss';

.pagination-bar {
    display: flex;
    flex-direction: row-reverse;
    width: 100%;
    background-color: #fff;
    padding-top: 16px;
    padding-bottom: 16px;
}
</style>