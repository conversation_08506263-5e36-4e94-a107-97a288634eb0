<script lang='ts' setup>
import { watch, ref } from 'vue'
import type { ICrmSendSmsParams } from '@/types/lead'

const props = defineProps<{
    visible: boolean
}>()
const dialogVisible = ref(false)
watch(() => props.visible, (newVal) => {
    dialogVisible.value = newVal
})
const form = ref<ICrmSendSmsParams>({})
const emit = defineEmits(['closeVisible'])
const handleClose = (val?:string) => {
    form.value = {}
    emit('closeVisible',val)
}


</script>
<template>
    <el-dialog
        v-model="dialogVisible"
        title="发送短信"
        width="500"
        show-close
        destory-on-close
        style="padding: 16px 24px"
        @close="handleClose('cancel')"
    >
        <el-form :model="form" label-position="top">
            <div class="b-margin-16">收信人 <span class="l-margin-16">9人</span></div>
            <el-form-item label="选择短信模板" v-model="form.smsModelId">
                <el-select placeholder="选择短信模板">
                    <el-option>模板一</el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="短信内容预览">
                <el-input type="textarea"></el-input>
            </el-form-item>
            <el-form-item style="margin-bottom: 0">
                <div style="width: 100%; display: flex; justify-content: flex-end">
                    <el-button style="margin-right: 16px" @click="handleClose('cancel')">取消</el-button>
                    <el-button type="primary" @click="handleClose()">提交</el-button>
                </div>
            </el-form-item>
        </el-form>
    </el-dialog>
</template>
<style scoped lang='scss'>
</style>
