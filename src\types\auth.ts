import type { IAllRecord } from './record'

export interface IAuthLogin {
    grant_type?: string
    password?: string
    refresh_token?: string
    scope?: string
    type?: string
    username?: string
    captchaKey?: string
}

export interface IAuthLoginResponse {
    access_token: string
    token_type: string
    refresh_token: string
    expires_in: number
    scope: string
    childOrgId: string
    defaultOrgId: string
    roleId: string
    permissions: string
    roleName: string
    tenantId: string
    nickname: string
    userId: string
    orgId: string
    username: string
    jti: string
    auth: number
    dataScope: Record<string, number>[]
}

export interface IAuthSysResponse {
    errCode: number
    errMsg: string
    success: boolean
    data: IAuthLoginResponse
}

export interface IAuthCaptchaCheckResponse {
    errCode: number
    errMsg: string
    success: boolean
    data: boolean
}

export interface IAuthForgetPasswordCodeRequest extends IAllRecord {
    phone: string
}
export interface IAuthForgetPasswordCodeResponse {
    data: object
}

export interface IAuthUpdatePasswordRequest {
    code: string
    mobile: string
    newPassword: string
    newPassword1: string
}

export interface IAuthMobileRequest {
    code: string
    mobile: string
}

export interface IAuthMobileCodeRequest extends IAllRecord {
    mobile: string
}

export interface IUpdatePasswordRequest {
    newPassword: string
    oldPassword: string
}

export interface IOrgSortResponse {
    errCode: number
    errMsg: string
    success: boolean
    data: object
}
