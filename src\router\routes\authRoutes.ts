import { type RouteRecordRaw } from 'vue-router'
import LoginView from '@/views/auth/LoginView.vue'

const authRoutes: Array<RouteRecordRaw> = [
    {
        path: '/login',
        name: 'login',
        component: LoginView,
        meta: { requiresAuth: false }, // 路由元信息
    },
    {
        path: '/forgot',
        name: 'forgot',
        component: LoginView,
        meta: { requiresAuth: false }, // 路由元信息
    },
    {
        path: '/phone-bind',
        name: 'phoneBind',
        component: LoginView,
        meta: { requiresAuth: false }, // 路由元信息
    },
    {
        path: '/phone-validate',
        name: 'phoneValidate',
        component: LoginView,
        meta: { requiresAuth: false }, // 路由元信息
    },
]

export default authRoutes
