<script lang="ts" setup>
import CreateTaskDrawer from '@/components/auto-dialer/create-task/CreateTaskDrawer.vue'
import outboundService from '@/service/outboundService'
import type { IAutoDialerListItem, IAutoDialerRequest } from '@/types/autoDialer'
import { ElMessage, ElMessageBox } from 'element-plus'
import { onMounted, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import permissionService from '@/service/permissionService'

const aiPhoneTaskOptions = [
    {
        key: 'taskName',
        type: 'input',
        placeholder: '请输入任务名称',
        label: '任务名称',
    },
    {
        key: 'create_date',
        type: 'date',
        placeholder: '请选择创建时间',
        label: '创建时间',
    },
    {
        key: 'status',
        type: 'select',
        placeholder: '请选择任务状态',
        label: '任务状态',
        options: [
            { label: '未开始', value: 1 },
            { label: '进行中', value: 2 },
            { label: '已完成', value: 3 },
            { label: '已终止', value: 4 },
            { label: '排队中', value: 5 },
            { label: '手动暂停', value: 6 },
            { label: '自动暂停', value: 7 },
            { label: '已过期', value: 8 },
        ],
    },
]

// ====================== Interfaces & Types ======================

// ====================== Props & Injections ======================
const props = defineProps<{
    filterParams?: IAutoDialerRequest
}>()

// ====================== Store & Computed Values ======================

// ====================== Refs & Reactive State ======================
const router = useRouter()
const requestParams = ref<IAutoDialerRequest>({})
const list = ref<IAutoDialerListItem[]>([])
const loading = ref(false)
const pageInfo = ref({
    page: 1,
    pageSize: 20,
    total: 0,
})
const createTaskVisible = ref(false)
const currentTask = ref<IAutoDialerListItem>()
const currentParams = ref<IAutoDialerRequest>({})

// ====================== Methods ======================
const getData = (params?: IAutoDialerRequest) => {
    loading.value = true

    if (params) {
        currentParams.value = params
    } else {
        params = currentParams.value
    }
    outboundService
        .taskPage({
            page: pageInfo.value.page,
            pageSize: pageInfo.value.pageSize,
            ...params,
        })
        .then((res) => {
            loading.value = false
            const { errCode, data, total } = res
            if (errCode === 0) {
                list.value = data
                pageInfo.value.total = total
            } else {
                list.value = []
            }
        })
        .catch(() => {
            loading.value = false
            list.value = []
            pageInfo.value.total = 0
        })
}

const formatStatus = (item: IAutoDialerListItem) => {
    if (!item) return '-'
    const target = aiPhoneTaskOptions.find((item) => {
        return item.key === 'status'
    })
    if (!target) return ''
    if (target.options && Array.isArray(target.options)) {
        return (
            target.options.find((e) => {
                return e.value === item.taskStatus
            })?.label || '-'
        )
    } else {
        return '-'
    }
}

const editTask = (item: IAutoDialerListItem, type: string) => {
    if (type === 'start') {
        startTask(item)
    }
    if (type === 'pause') {
        pauseTask(item)
    }
    if (type === 'termination') {
        terminationTask(item)
    }
    if (type === 'delete') {
        deleteTask(item)
    }
}

const startTask = (item: IAutoDialerListItem) => {
    outboundService
        .taskStart({ taskCode: item.taskCode, tenantId: item.tenantId })
        .then((res) => {
            const { errCode, errMsg } = res
            if (errCode === 0) {
                ElMessage.success('任务已启动')
                getData()
            } else {
                ElMessage.error(errMsg || '任务启动失败')
            }
        })
        .catch(() => {
            ElMessage.error('系统错误，任务启动失败')
        })
}

const deleteTask = (item: IAutoDialerListItem) => {
    ElMessageBox.confirm('是否确认删除该任务？删除后不可恢复', '删除任务', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(() => {
            outboundService.taskDelete({ taskCode: item.taskCode, tenantId: item.tenantId }).then((res) => {
                const { errCode, errMsg } = res
                if (errCode === 0) {
                    ElMessage.success('删除成功')
                    getData()
                } else {
                    ElMessage.error(errMsg || '任务删除失败')
                }
            })
        })
        .catch(() => {})
}

const terminationTask = (item: IAutoDialerListItem) => {
    ElMessageBox.confirm('是否确认终止该任务？终止后无法启动', '终止任务', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(() => {
            outboundService.taskTermination({ taskCode: item.taskCode, tenantId: item.tenantId }).then((res) => {
                const { errCode, errMsg } = res
                if (errCode === 0) {
                    ElMessage.success('终止成功')
                    getData()
                } else {
                    ElMessage.error(errMsg || '终止失败')
                }
            })
        })
        .catch(() => {})
}

const pauseTask = (item: IAutoDialerListItem) => {
    outboundService
        .taskPause({ taskCode: item.taskCode, tenantId: item.tenantId })
        .then((res) => {
            const { errCode, errMsg } = res
            if (errCode === 0) {
                ElMessage.success('任务已暂停')
                getData()
            } else {
                ElMessage.error(errMsg || '任务暂停失败')
            }
        })
        .catch(() => {
            ElMessage.error('系统错误，任务暂停失败')
        })
}

const editTaskDrawer = (item: IAutoDialerListItem) => {
    currentTask.value = item
    createTaskVisible.value = true
}

const toTaskDetail = (item: IAutoDialerListItem) => {
    const route = router.resolve({
        name: 'dialing-task-detail',
        query: {
            taskCode: item.taskCode,
        },
    })
    window.open(route.href, '_blank')
}

const refreshAfterEdit = () => {
    getData()
}

// ====================== Watchers ======================
watch(
    () => props.filterParams,
    (value) => {
        if (value) {
            getData(value)
        }
    },
    {
        deep: true,
    }
)

// ====================== Lifecycle Hooks ======================
onMounted(() => {
    getData(requestParams.value)
})
</script>

<template>
    <div class="flex flex-column height-100 dialing-task-table">
        <div class="flex flex-1" style="overflow: hidden">
            <div class="flex flex-row gap-16 top-bottom-center"></div>

            <el-table :data="list" style="width: 100%" v-loading="loading" height="100%">
                <el-table-column prop="taskName" label="任务名称" />
                <el-table-column prop="progress.totalCompleted" label="已接通" width="80">
                    <template #default="scope">
                        {{ scope.row.connectedCount }}
                    </template>
                </el-table-column>
                <el-table-column prop="progress.cancelledNum" label="未接通" width="80">
                    <template #default="scope">
                        {{ scope.row.missedCount }}
                    </template>
                </el-table-column>
                <el-table-column prop="creationTime" label="创建时间" width="170">
                    <template #default="scope">
                        {{ scope.row.createTime }}
                    </template>
                </el-table-column>
                <el-table-column prop="dialogTaskName" label="机器人话术" />
                <el-table-column prop="taskStatus" label="任务状态">
                    <template #default="scope">
                        {{ formatStatus(scope.row) }}
                    </template>
                </el-table-column>
                <el-table-column prop="taskDesc" label="任务描述" />
                <el-table-column label="操作" fixed="right" width="100" align="left">
                    <template #default="scope">
                        <div class="display-flex top-bottom-center space-between">
                            <a
                                class="color-blue pointer"
                                style="text-decoration: none"
                                @click="toTaskDetail(scope.row)"
                            >
                                详情
                            </a>
                            <el-dropdown v-if="[1, 2, 6, 7].includes(scope.row.taskStatus) && ( (scope.row.taskStatus === 6 && permissionService.isOutboundtaskStartPermitted()) || ( scope.row.taskStatus === 7 || scope.row.taskStatus === 2 ) && permissionService.isOutboundtaskPausePermitted() || [1, 6, 7].includes(scope.row.taskStatus) && permissionService.isOutboundtaskEditPermitted() || [1, 6, 7].includes(scope.row.taskStatus) && permissionService.isOutboundtaskAbortPermitted() || scope.row.taskStatus == 1 && permissionService.isOutboundtaskDeletePermitted())">
                                <a class="color-blue pointer" style="text-decoration: none"> 更多 </a>
                                <template #dropdown>
                                    <el-dropdown-menu>
                                        <el-dropdown-item v-if="scope.row.taskStatus === 6 && permissionService.isOutboundtaskStartPermitted()">
                                            <el-button type="primary" text @click="editTask(scope.row, 'start')">
                                                开始
                                            </el-button>
                                        </el-dropdown-item>
                                        <el-dropdown-item
                                            v-if="( scope.row.taskStatus === 7 || scope.row.taskStatus === 2 ) && permissionService.isOutboundtaskPausePermitted()"
                                        >
                                            <el-button type="primary" text @click="editTask(scope.row, 'pause')">
                                                暂停
                                            </el-button>
                                        </el-dropdown-item>
                                        <el-dropdown-item v-if="[1, 6, 7].includes(scope.row.taskStatus) && permissionService.isOutboundtaskEditPermitted()">
                                            <el-button type="primary" text @click="editTaskDrawer(scope.row)">
                                                编辑
                                            </el-button>
                                        </el-dropdown-item>
                                        <el-dropdown-item v-if="[1, 6, 7].includes(scope.row.taskStatus) && permissionService.isOutboundtaskAbortPermitted()">
                                            <el-button type="primary" text @click="editTask(scope.row, 'termination')">
                                                终止
                                            </el-button>
                                        </el-dropdown-item>
                                        <el-dropdown-item v-if="scope.row.taskStatus == 1 && permissionService.isOutboundtaskDeletePermitted()" >
                                            <el-button type="primary" text @click="editTask(scope.row, 'delete')">
                                                删除
                                            </el-button>
                                        </el-dropdown-item>
                                    </el-dropdown-menu>
                                </template>
                            </el-dropdown>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <el-affix position="bottom" :offset="0">
            <div class="display-flex top-bottom-center all-padding-18 justify-flex-end back-color-white">
                <el-pagination
                    :hide-on-single-page="true"
                    v-model:currentPage="pageInfo.page"
                    v-model:page-size="pageInfo.pageSize"
                    :page-sizes="[10, 20, 30, 40, 50, 100]"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="pageInfo.total"
                    @size-change="getData"
                    @current-change="getData"
                />
            </div>
        </el-affix>
        <CreateTaskDrawer
            v-model:visible="createTaskVisible"
            :crm-id="[]"
            :current-task="currentTask"
            :refresh-after-edit="refreshAfterEdit"
        />
    </div>
</template>

<style lang="scss" scoped>
.dialing-task-table .el-table {
    --el-table-header-bg-color: var(--table-bg-2);
    --el-table-header-text-color: var(--main-black);
    --el-font-size-base: 16px;
}
</style>
