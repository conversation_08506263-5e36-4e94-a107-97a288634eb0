<script lang='ts' setup>
import { computed, ref, onMounted, reactive, watch } from 'vue'

import aicService from '@/service/aicService'
import type { IUserListResponse } from '@/types/user'
import type { ILeadData } from '@/types/lead'
import type { FormInstance } from 'element-plus'
import customService from '@/service/customService'
import { ElMessage } from 'element-plus'

const props = defineProps<{
    visible: boolean
    selectedLeadList: ILeadData[]
}>()
const selectedLeadIds = computed(() => {
    return props.selectedLeadList.map((item) => {
        return item.id
    })
})
const hasAnyMinistrant = computed(() => {
    return props.selectedLeadList.some((item) => {
        return item.ministrantInfos && item.ministrantInfos.length>0
    })
})
const dialogVisible = ref(props.visible)
watch(() => props.visible, (newVal) => {
    dialogVisible.value = newVal
})

const emit = defineEmits(['closeVisible'])
const handleClose = (val?: string) => {
    form.addUser = []
    form.delUser = []
    form.replaceFrom = ''
    form.replaceTo = ''
    emit('closeVisible', val)
}
const ministrantType = ref('add')
const ministrantList = ref<IUserListResponse[]>([])
const hadMinistrantList = computed(() => {
    const allHadMinistrantList = props.selectedLeadList.map((item => item.ministrantInfos)).flat()
    const uniqueMinistrantInfos = Array.from(
        new Set(allHadMinistrantList.map(info => info.id))
    ).map(id => {
        return allHadMinistrantList.find(info => info.id === id)!
    })
    return uniqueMinistrantInfos
})
const getMinistrantList = async () => {
    let res = await aicService.conditionGetUser()
    ministrantList.value = res
}
onMounted(() => {
    getMinistrantList()
})
type formType = {
    leadIds: string[]
    addUser?: string[],
    delUser?: string[],
    replaceFrom?: string,
    replaceTo?: string
}
const form = reactive({
    addUser: [],
    delUser: [],
    replaceFrom: '',
    replaceTo: ''
})
const formRef = ref<FormInstance>()
const submitForm = async () => {
    let obj = reactive<formType>(
        {
            leadIds: selectedLeadIds.value
        }
    )
    if (ministrantType.value === 'add') {
        obj.addUser = form.addUser
    } else if (ministrantType.value === 'del') {
        obj.delUser = form.delUser
    } else {
        obj.replaceFrom = form.replaceFrom
        obj.replaceTo = form.replaceTo
    }
    try {
        await customService.customUpdateMinistrant(obj)
        ElMessage.success('操作成功')
        handleClose()
    } catch (error) {
        console.log('操作协作人失败', error)
    }
}
</script>
<template>
    <el-dialog
        v-model="dialogVisible"
        title="协作人"
        width="500"
        show-close
        destroy-on-close
        style="padding: 16px 24px"
        @close="handleClose('cancel')"
    >
        <el-form ref="formRef" :model="form" label-position="top">
            <el-form-item label="操作">
                <el-radio-group v-model="ministrantType">
                    <el-radio value="add">增加</el-radio>
                    <el-radio :disabled="!hasAnyMinistrant" value="reset">替换</el-radio>
                    <el-radio :disabled="!hasAnyMinistrant" value="del">移除</el-radio>
                </el-radio-group>
            </el-form-item>
            <!-- 添加协作人-->
            <el-form-item v-if="ministrantType==='add'" label="选择协作人">
                <el-select v-model="form.addUser" placeholder="请选择协作人" clearable multiple value-key="_id">
                    <el-option v-for="item in ministrantList" :key="item.id" :label="item.nickname" :value="item.id"></el-option>
                </el-select>
            </el-form-item>
            <!-- 替换协作人 -->
            <el-form-item v-if="ministrantType==='reset'" label="选择协作人">
                <el-select v-model="form.replaceFrom" placeholder="请选择需要替换的协作人" clearable value-key="_id">
                    <el-option v-for="item in hadMinistrantList" :key="item.id" :label="item.nickname" :value="item.id"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item v-if="ministrantType==='reset'" label="替换成">
                <el-select v-model="form.replaceTo" placeholder="请选择替换后协作人" clearable value-key="_id">
                    <el-option v-for="item in ministrantList" :key="item.id" :label="item.nickname" :value="item.id"></el-option>
                </el-select>
            </el-form-item>
            <!-- 移除协作人 -->
            <el-form-item v-if="ministrantType==='del'" label="选择协作人">
                <el-select v-model="form.delUser" placeholder="请选择需要移除的协作人" clearable multiple value-key="_id">
                    <el-option v-for="item in hadMinistrantList" :key="item.id" :label="item.nickname" :value="item.id"></el-option>
                </el-select>
            </el-form-item>

            <el-form-item style="margin-bottom: 0">
                <div style="width: 100%; display: flex; justify-content: flex-end">
                    <el-button style="margin-right: 16px" @click="handleClose('cancel')">取消</el-button>
                    <el-button type="primary" @click="submitForm()">保存</el-button>
                </div>
            </el-form-item>
        </el-form>
    </el-dialog>
</template>
<style scoped lang='scss'>
</style>
