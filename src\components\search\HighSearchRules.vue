<template>
    <div class="display-flex flex-column height-100">
        <div class="model-title display-flex top-bottom-center color-blue font-14" v-if="activeTemplete && !isAdmin">
            <div>
                <Icon icon="icon-jianzhu02-F" :color="commonColor.mainBlue" size="14px" class="r-margin-5" />
            </div>
            <div class="t-margin-5">
                以下是： {{ activeTemplete.name }} 的企业
            </div>
        </div>
        <div v-show="(!activeTemplete || !activeTemplete.isModel) || isAdmin"
             class="height-100 display-flex flex-column">
            <div class="t-margin-16 display-flex width-100">
                <el-dropdown trigger="click">
                    <el-button :icon="CirclePlusFilled" type="primary">添加</el-button>
                    <template #dropdown>
                        <el-dropdown-menu>
                            <el-dropdown-item @click="addCondition('only')">单个条件</el-dropdown-item>
                            <el-dropdown-item @click="addCondition('group')"> 条件组 </el-dropdown-item>
                        </el-dropdown-menu>
                    </template>
                </el-dropdown>
            </div>
            <div style="height: calc(100% - 100px);">
                <div class="high-search-rules-box t-margin-16 l-padding-30 relative flex-grow-1 height-100"
                     v-if="hightSearchRulesData.length">
                    <el-scrollbar ref="highSearchRulesScroll">
                        <div class="high-search-rules-item r-padding-10">
                            <div class="display-flex gap-10 top-bottom-center tb-padding-8 l-padding-10"
                                 v-if="searchConditions.type === 'branch' || searchConditions.type == 'SHOULD'">
                                <div class="text-nowrap">满足</div>
                                <div class="operator-box">
                                    <el-select v-model="searchConditions.operator">
                                        <el-option label="全部条件" value="MUST" />
                                        <el-option label="任一条件" value="SHOULD" />
                                    </el-select>
                                </div>
                            </div>
                            <div class="t-margin-10 l-padding-60"
                                 v-for="(condition, index) in searchConditions.children" :key="condition.prop">
                                <!-- 条件组 -->
                                <template v-if="condition.children && condition.children.length > 0">
                                    <div class="l-padding-10 group-node relative">
                                        <div class="display-flex gap-10 top-bottom-center"
                                             v-if="searchConditions.type === 'branch' || searchConditions.type == 'SHOULD'">
                                            <div class="text-nowrap">满足</div>
                                            <div class="operator-box">
                                                <el-select v-model="condition.operator">
                                                    <el-option label="全部条件" value="MUST" />
                                                    <el-option label="任一条件" value="SHOULD" />
                                                </el-select>
                                            </div>
                                            <div>
                                                <el-icon class="pointer" @click="removeCondition(index)">
                                                    <Delete />
                                                </el-icon>
                                            </div>
                                        </div>
                                        <div v-for="(childCondition, idx) in condition.children"
                                             :key="childCondition.prop"
                                             class="high-search-rules-item-title l-padding-30">
                                            <ConditionItem :conditionItem="childCondition" @updateCondition="
                                                (data) => {
                                                    updateCondition(data, 'group', index, idx)
                                                }
                                            " @removeCondition="removeCondition(idx, condition.children, index)" />
                                        </div>

                                        <div class="l-padding-30">
                                            <div class="condition-item  relative  t-padding-10">
                                                <el-button @click="
                                                    addGroupConditionChild(condition)
                                                ">添加条件</el-button>
                                            </div>
                                        </div>
                                    </div>
                                </template>
                                <!-- 单个条件 -->
                                <template v-else>
                                    <div class="only-node relative">
                                        <ConditionItem :conditionItem="condition" @updateCondition="
                                            (data) => {
                                                updateCondition(data, 'only', index)
                                            }
                                        " @removeCondition="removeCondition(index)" />
                                    </div>

                                </template>
                            </div>
                        </div>
                    </el-scrollbar>
                </div>

            </div>
            <div class="display-flex t-margin-15 justify-flex-end width-100"
                 v-if="!isAdmin && (!activeTemplete || !activeTemplete.isModel)">
                <el-button @click="clearRules()">清空所选</el-button>
                <el-button type="primary" @click="editTemplete()"
                           v-if="activeTemplete?.id && !activeTemplete.isModel">保存模板
                </el-button>
                <el-button type="primary" @click="saveNewTemplete()"
                           v-if="!activeTemplete || !activeTemplete.isModel">另存新模板
                </el-button>
                <el-button type="primary" @click="hanldeSearch()">执行搜索</el-button>
            </div>

        </div>

        <div class="flex-grow-1 hide-templete" v-if="(activeTemplete && activeTemplete.isModel) && !isAdmin">

        </div>


    </div>

</template>

<script lang='ts' setup>
import { CirclePlusFilled } from '@element-plus/icons-vue'
import { ref, onMounted, getCurrentInstance, defineEmits, defineProps, watch, nextTick, defineExpose } from 'vue'
import type { Ref } from 'vue'
import type { IHighSearchRules, ISearchConditions } from '@/types/model'
import type { conditionItem, ISearchGetTemplateItem } from '@/types/company'
import { useStore } from 'vuex'
import aicService from '@/service/aicService'
import ConditionItem from './condition/ConditionItem.vue'
import { ElMessage, ElMessageBox } from 'element-plus'

import { setItem, getItem } from '@/utils/storage'
const store = useStore()

const cacheHighSearchRulesData = store.state.app.hightSearchRulesData

const hightSearchRulesData: Ref<IHighSearchRules[]> = ref([])

const emits = defineEmits(['search', 'clearTemplete', 'updateTemplete'])

// const props = defineProps({
//     activeTemplete: {
//         type: Object as PropType<Record<string, unknown> | null>,
//         default: null
//     }
// })

const props = withDefaults(defineProps<{
    activeTemplete: ISearchGetTemplateItem | null,
    isAdmin: boolean
}>(), {
    activeTemplete: null,
    isAdmin: false
})

const instance = getCurrentInstance()
const commonColor = ref(instance?.appContext.config.globalProperties.$commom.color)
const getHighSearchRulesData = () => {
    if (cacheHighSearchRulesData) {
        hightSearchRulesData.value = cacheHighSearchRulesData
    } else {
        Promise.all([aicService.conditionGetInfo({ searchType: 'highSearch' }), aicService.conditionGetData({})]).then(
            ([rulesRes, staticConfigRes]) => {
                console.log(staticConfigRes)
                hightSearchRulesData.value = rulesRes || []
                store.commit('app/SET_HIGHT_SEARCH_RULES_DATA', rulesRes)
                store.commit('app/SET_STATIC_CONFIG', staticConfigRes)
            }
        )
    }
}

const originalCondion = {
    operator: 'MUST',
    prop: '',
    type: 'branch',
    value: [],
    children: [{
        prop: '',
        type: 'branch',
        operator: 'IN',
        value: []
    }]
}



const searchConditions: Ref<ISearchConditions> = ref(JSON.parse(JSON.stringify(originalCondion)))

const clearRules = () => {
    emits('clearTemplete')
    searchConditions.value = JSON.parse(JSON.stringify(originalCondion))
}



watch(props, (nVal) => {
    console.log('nval', nVal)
    if (nVal.activeTemplete?.searchData?.list?.length) {
        searchConditions.value = nVal.activeTemplete.searchData.list[0]
        nextTick(() => {
            hanldeSearch()
        })
    }
})


//添加单独条件

const addCondition = (type: string) => {
    if (searchConditions.value.children) {
        let baseObj = {
            operator: 'IN',
            prop: '',
            value: [],
        }
        let obj: ISearchConditions = {
            ...baseObj,
        }
        if (type === 'group') {
            //条件组
            obj['operator'] = 'MUST'
            obj['children'] = [
                {
                    type: 'leaf',
                    ...baseObj,
                },
            ]
        } else {
            //添加单一条件
            obj['type'] = 'branch'
        }
        searchConditions.value.children.push(obj)
        nextTick(() => {
            highSearchRulesScroll.value.setScrollTop(9999999)
        })
    } else {
        ElMessage({
            message: '数据错误，请删除模板',
            type: 'warning',
        })
    }
}

const addGroupConditionChild = (condition: ISearchConditions) => {
    condition.children?.push({
        type: 'leaf',
        prop: '',
        operator: 'IN',
        value: [],
    })
}

const removeCondition = (index: number, conditions: ISearchConditions[] | null = null, groupIndex: number = 0) => {
    if (conditions) {
        if (conditions.length === 1) {
            // ElMessage({
            //     message: '条件组不可为空',
            //     type: 'warning',
            // })
            removeCondition(groupIndex)
            return
        }
        conditions.splice(index, 1)
    } else {
        searchConditions.value.children?.splice(index, 1)
    }
}

const updateCondition = (data: ISearchConditions, type: string, index: number, idx: number = 0) => {
    console.log(data)
    let conditions = searchConditions.value.children
    if (!conditions || !conditions.length) {
        return
    }
    if (type === 'group' && conditions[index].children) {
        conditions[index].children[idx] = data
    }
    if (type === 'only' && conditions.length) {
        conditions[index] = data
    }
}

const trSearchRules = (data: ISearchConditions) => {
    const cacheHighSearchRulesData = store.state.app.hightSearchRulesData
    let traverse = (nodes: ISearchConditions[]) => {
        let res = nodes.map(node => {
            let result: conditionItem = {
                cn: '',
                cr: '',
                cv: []
            }

            console.log(node)

            let dataType = node.dataType

            let operator = ['number', 'date'].includes(dataType || '') ? 'BETWEEN' : node.operator



            if (node.children && node.children.length) {
                result.cn = 'composite'
                result.cr = operator
                result.cv = traverse(node.children)
            } else {
                result.cn = node.prop
                result.cr = operator


                if (node.prop === 'industry') {
                    result.cv = node.valueLabel || []
                } else {
                    console.log(node.value)
                    let nValue = JSON.parse(JSON.stringify(node.value))
                    if (dataType === 'select') {
                        if (nValue === '1') {
                            nValue = true
                        } else {
                            nValue = false
                        }
                        //将单选值转换成布尔值
                    } else if (operator === 'BETWEEN') {
                        nValue = [`${nValue[0]}-${nValue[1]}`]
                    }
                    if (Array.isArray(nValue)) {
                        if (dataType === 'mapped') {
                            cacheHighSearchRulesData.forEach((group: IHighSearchRules) => {
                                if (group.children) {
                                    let res = group.children.find(item => { return item.key === node.prop })
                                    if (res && res.levelConfig) {
                                        result.cv = {}
                                        res.levelConfig.levels.forEach((level: { name: string, title: string }, idx: number) => {
                                            console.log('nValue', nValue)
                                            let nowLevelVal = nValue.filter(vItem => {
                                                return vItem.length === idx + 1
                                            })
                                            if (nowLevelVal.length) {
                                                let valArr = nowLevelVal.map(i => { return i[idx] }).flat();
                                                (result.cv as { [key: string]: unknown })[level.name] = [...new Set(valArr)]
                                            }
                                        })
                                    }
                                }
                            })
                        } else {
                            result.cv = [...new Set(nValue.flat())]
                        }
                    } else {
                        result.cv = nValue
                    }
                }
            }
            return result

        })

        return res
    }

    // 从根节点开始转换
    return {
        cn: 'composite',
        cr: 'MUST',
        cv: traverse(data.children || [])
    }

}



const hanldeSearch = () => {

    console.log('searchConditions.value', searchConditions.value)

    //将组件格式转换为服务端需要的格式
    if (searchConditions.value.children?.length) {
        let trConditions: conditionItem = trSearchRules(searchConditions.value)

        console.log('trConditions', trConditions)

        if (trConditions?.cv && Array.isArray(trConditions.cv)) {
            trConditions.cv.filter((item) => {
                if (typeof item === 'object' && 'cv' in item && (Array.isArray(item.cv) && item.cv?.length)) {
                    return true
                } else {
                    return false
                }
            })

            if (!props.activeTemplete?.isModel) {
                //非系统模板，每次搜索后都记录localStorage缓存

                setItem('advanceSearchRules', searchConditions.value)

            }
            emits('search', trConditions)
        }

    } else {
        //没有条件
        ElMessage({
            message: '还没有设置条件',
            type: 'warning',
        })
    }


}

const editTemplete = () => {
    console.log(searchConditions.value)
    if (!searchConditions.value.children?.length) {
        ElMessage({
            message: '还没有设置条件,不能保存空模板',
            type: 'warning',
        })
        return
    }
    aicService.searchUpdateTemplate({
        templateId: props.activeTemplete?.id as string,
        searchData: {
            list: [searchConditions.value]
        }
    }).then(() => {
        ElMessage({
            type: 'success',
            message: '保存成功',
        })
        emits('updateTemplete')
    })

}

const saveNewTemplete = () => {
    if (!searchConditions.value.children?.length) {
        ElMessage({
            message: '还没有设置条件,不能保存空模板',
            type: 'warning',
        })
        return
    }
    ElMessageBox.prompt('请输入模板名称', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern:
            /^(?!\s*$).{1,20}$/,
        inputErrorMessage: '模板名称为0-20个字符',
    })
        .then(({ value }) => {
            //存模板逻辑
            console.log(value)
            aicService.searchSaveTemplate({
                name: value,
                searchData: {
                    list: [searchConditions.value]
                }
            }).then(() => {
                ElMessage({
                    type: 'success',
                    message: '保存成功',
                })
                emits('updateTemplete')
            })
        })
        .catch(() => {

        })
}

const highSearchRulesScroll = ref()

defineExpose({ searchConditions })

onMounted(() => {
    getHighSearchRulesData()
    if (props.activeTemplete?.searchData?.list?.length) {
        //如果有规则，则直接赋值
        searchConditions.value = props.activeTemplete.searchData.list[0]

        console.log(' searchConditions.value', searchConditions.value)
        nextTick(() => {
            hanldeSearch()
        })
    } else {
        let rules = getItem('advanceSearchRules')
        if (rules) {
            searchConditions.value = rules
            nextTick(() => {
                hanldeSearch()
            })
        }
    }
})
</script>

<style lang='scss' scoped>
.hide-templete {
    background: url('../../assets/images/hide-templete.png') no-repeat;
    background-position: center;
    background-size: contain;
}

.operator-box {
    width: 140px;
    --el-fill-color-blank: #F6F8FA;
}

.group-node:before {
    content: "";
    display: block;
    width: 0;
    height: calc(100% - 30px);
    left: -4px;
    top: 12px;
    position: absolute;
    border-left: 1px dashed var(--three-grey);
}

.group-node::after {
    content: "";
    display: block;
    width: 50px;
    height: 1px;
    left: -60px;
    top: 12px;
    position: absolute;
    border-top: 1px dashed var(--three-grey);
}

.group-node .condition-item::before {
    content: "";
    display: block;
    width: 0;
    width: 30px;
    height: 1px;
    left: -40px;
    top: 25px;
    position: absolute;
    border-top: 1px dashed var(--three-grey);
}

.only-node:before {
    content: "";
    display: block;
    width: 50px;
    height: 1px;
    left: -60px;
    top: 25px;
    position: absolute;
    border-top: 1px dashed var(--three-grey);
}

.high-search-rules-box {
    // height: calc(100% - 100px);
}

.high-search-rules-box:before {
    content: "";
    display: block;
    width: 1px;
    height: calc(100% - 42px);
    left: 27px;
    top: 18px;
    position: absolute;
    border-left: 1px dashed var(--three-grey);
}
</style>