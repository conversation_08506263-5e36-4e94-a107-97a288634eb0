const mqttUrl = `${window.location.host.indexOf('localhost') > -1 || window.location.host.indexOf('dev') > -1 ? 'wss://zqytest.shuzutech.com:8688/ws' : 'wss://smartdvp.shuzutech.com/ws'}`

export default {
    cancleConnect() {
        if (window.wsClient) {
            window.wsClient.end()
        }

    },
    createConnect(userId) {
        try {
            const brokerUrl = mqttUrl
            console.log('brokerUrl', brokerUrl)
            if (!brokerUrl) {
                return
            }

            window.wsClient = window.mqtt.connect(brokerUrl)
            window.wsClient.on('connect', () => {
                // console.log('链接消息中心成功',`user/${userId}`);
                let userUrl= `user/${userId}`
                // 订阅某个主题
                window.wsClient.subscribe(['sys/notice/zqy',userUrl], (err) => {
                    if (!err) {
                        console.log('订阅成功')
                    }else{
                        throw new Error('订阅失败')
                    }
                })
            })

        
            window.wsClient.on('error', (error) => {
                throw new Error('链接消息中心失败:', error)
            })
        } catch ({
            message
        }) {
            console.log(message)
        }

    }
}