<template>
    <div class="relative">
        <div class="search-bar">
            <el-scrollbar>
                <div class="display-flex">
                    <div class="search-bar-item pointer tb-padding-10 lr-padding-16 display-flex left-right-center"
                         style="width: 120px;min-width: none;">
                        <el-dropdown trigger="click">
                            <div class="display-flex top-bottom-center">
                                <span> {{ searchParams.scope === 'address' ? '地址定位' : '企业名称' }}</span>
                                <el-icon><arrow-down /></el-icon>
                            </div>
                            <template #dropdown>
                                <el-dropdown-menu>
                                    <el-dropdown-item @click="scopeChange('address')">地址定位</el-dropdown-item>
                                    <el-dropdown-item @click="scopeChange('companyname')">企业名称</el-dropdown-item>
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>
                    </div>
                    <div class="search-bar-item  display-flex top-bottom-center flex-1">
                        <el-autocomplete ref="querySearchRef" v-model="searchParams.keyword"
                                         :fetch-suggestions="querySearch" :trigger-on-focus="false" clearable class="inline-input"
                                         :placeholder="searchParams.scope == 'address' ? '输入地址将为您推荐附近的企业' : '请输入企业名称'"
                                         @select="handleSelect" @focus="handleQuerySearch">
                            <template #default="{ item }">
                                <div>{{ item.name }}</div>
                                <div v-if="searchParams.scope == 'address'"
                                     class="font-four-level font-mini display-flex relative top-bottom-center"
                                     style='margin-top:-10px'>
                                    <el-icon color="#1966FF">
                                        <LocationInformation />
                                    </el-icon>{{ item.address }}
                                </div>
                            </template>
                            <!-- <template #suffix>
							<el-button type="primary" :icon="Search" @click="handleIconClick" color="#1966ff" />
						</template> -->
                        </el-autocomplete>
                    </div>
                    <div class="search-bar-item pointer  display-flex top-bottom-center">
                        <el-cascader ref="cityRef" v-model="searchParams.filter.location" :options="cityList"
                                     placeholder="所属地区" @change="choseLoaction" :props="{ checkStrictly: true }" />
                    </div>
                    <div class="search-bar-item pointer  display-flex top-bottom-center">
                        <el-cascader ref="industry" v-model="searchParams.filter.industry" collapse-tags
                                     collapse-tags-tooltip :options="industryList"
                                     :props="{ checkStrictly: true, multiple: true }" placeholder="所属行业" />
                    </div>
                    <div class="search-bar-item pointer  display-flex top-bottom-center">
                        <el-select v-model="searchParams.filter.contact" :empty-values="['']" :value-on-clear="''"
                                   class="select-label-wrap" size="large" placeholder="联系方式">
                            <el-option v-for="item in contactList" :key="item.value" :label="item.label"
                                       :value="item.value"></el-option>
                        </el-select>
                    </div>
                    <div class="search-bar-item pointer  display-flex top-bottom-center">
                        <el-select v-model="searchParams.filter.entstatus" size="large" :empty-values="['']"
                                   :value-on-clear="''" placeholder="营业状态">
                            <el-option v-for="item in statusList" :key="item.value" :label="item.label"
                                       :value="item.value"></el-option>
                        </el-select>
                    </div>
                    <div class="search-bar-item pointer  display-flex top-bottom-center">
                        <el-date-picker v-model="searchParams.filter.establishment" type="daterange" unlink-panels
                                        range-separator="~" start-placeholder="成立时间" end-placeholder="成立时间" format="YYYY/MM/DD"
                                        value-format="x" ref="datePicker" style="border: none" />
                    </div>
                    <div class="search-bar-item pointer  display-flex top-bottom-center">
                        <el-select v-model="searchParams.filter.registercapital" size="large" :empty-values="['']"
                                   :value-on-clear="''" placeholder="注册资金">
                            <el-option v-for="item in capitalList" :key="item.value" :label="item.label"
                                       :value="item.value"></el-option>
                        </el-select>
                    </div>
                    <div class="search-bar-item pointer  display-flex top-bottom-center">
                        <el-select v-model="searchParams.filter.enttype" size="large" :empty-values="[]"
                                   :value-on-clear="[]" placeholder="企业类型" multiple collapse-tags collapse-tags-tooltip
                                   @change='changeAll'>
                            <el-option v-for="item in enterpriseTypeList" :key="item.value" :label="item.label"
                                       :value="item.value"></el-option>
                        </el-select>
                    </div>
                </div>
            </el-scrollbar>
        </div>
        <Map ref="map" @setMapInfo="setMapInfo" />
        <EnterpriseMapInfoList style="position: absolute; top: 41px; z-index: 2000" ref="enterpriseMapInfoList"
                               v-show="searchParams.keyword || zoomLevel === 4" :resultList="companyList" :listTotal="listTotal"
                               @getTransferList="getTransferList" @pageChange="pageChange" @transferCheck="transferCheck"
                               :radius="searchData?.filter?.circle?.radius || 5" />
        <TransferCrmDialog v-model:visible="showBatchTransfer" :selected="multipleSelection"
                           :success="transferCrmSuccess" />
    </div>

</template>

<script lang='ts' setup>

import { ElMessage } from 'element-plus'
import { ref, onMounted, reactive, computed, getCurrentInstance, watch } from 'vue'
import type {
    ICompanyInfo
} from '@/types/company'
import type { Ref } from 'vue'
import { useStore } from 'vuex'
import Map from './components/Map.vue'
import aicService from '@/service/aicService'
import EnterpriseMapInfoList from './components/EnterpriseMapInfoList.vue'
const store = useStore()
const instance = getCurrentInstance()
const companyList = ref([])

const enterpriseMapInfoList = ref()

const listTotal = ref(0)
const multipleSelection = ref<ICompanyInfo[]>([])
const zoom: Ref<number> = ref(0)

const showBatchTransfer: Ref<boolean> = ref(false)

const map = ref(null)

const cityRef = ref(null)

const industry = ref(null)

const searchParams = reactive({
    scope: 'address',
    keyword: '',
    page: 1,
    pageSize: 20,
    filter: {
        syncRobotRangeDate: [],
        location: [],
        industryshort: [],
        secindustryshort: [],
        circle: null,
        filterSync: 0,
        filterUnfold: 0,
        filterSyncRobot: 0,
        fourthIndustryShort: [],
        thirdIndustryShort: [],
        contact: '', // 联系方式
        entstatus: '', // 营业状态
        establishment: [],
        registercapital: '', // 注册资金
        enttype: [],
        industry: []
    }
})

watch(searchParams, () => {
    getLxyData()
})

const cityList = computed(() => {
    return store.state.app.staticConfig.area
})

const industryList = computed(() => {
    return store.state.app.staticConfig.industry
})

const contactList = computed(() => {
    return instance?.appContext.config.globalProperties.$commom.commonData.mapContactListEnum
})

const statusList = computed(() => {
    return instance?.appContext.config.globalProperties.$commom.commonData.mapStatusListEnum
})


const capitalList = computed(() => {
    return instance?.appContext.config.globalProperties.$commom.commonData.mapCapitalListEnum
})

const enterpriseTypeList = computed(() => {
    return instance?.appContext.config.globalProperties.$commom.commonData.mapEnterpriseTypeListEnum
})


const zoomLevel = computed(() => {
    let v = zoom.value
    if (v > 12) {
        //街道
        return 4
    } else if (v > 10) {
        //区级
        return 3
    } else if (v > 6) {
        //市级
        return 2
    } else {
        //省级
        return 1
    }
})
const setMapInfo = (params) => {
    zoom.value = params.zoom
    console.log(params)
    let code = params.location.ad_info.adcode
    let location = params.location.location
    let codes = [code.slice(0, 2), code.slice(0, 4)]
    if (zoom.value > 12) {
        codes.push(code)
    }
    console.log(zoomLevel)

    searchParams.filter.location = codes.slice(0, zoomLevel.value - 1)
    searchParams.filter.circle = {
        center: `${location.lat},${location.lng}`,
        radius: params.radius ? parseInt(params.radius) / 1000 : 5
    }

    // this.$refs['list'].$data.page = 1;

    enterpriseMapInfoList.value.pageInfo.page = 1

    searchParams.page = 1

    getLxyData()
    // this.getLxyData();
}

const searchData = computed(() => {
    let obj = JSON.parse(JSON.stringify(searchParams))
    let filter = obj.filter
    let model = map.value?.model || ''
    //将自己数据格式转换成励晓云的数据
    //转换地址，取最后一位
    filter.location = [filter.location[filter.location.length - 1]]
    if (zoomLevel.value === 1) {
        filter.location = ['0']
    }
    if (filter.industry.length) {
        let nodes = industry.value.getCheckedNodes()
        filter.industry = nodes.map((node) => {
            return `${node.value},${node.label},${node.level}`
        })
        console.log('nnnn', nodes)
    }
    if (filter.establishment?.length && filter.establishment.length === 2) {
        filter.establishment = [`${filter.establishment[0]}-${filter.establishment[1]}`]
    } else {
        filter.establishment = []
    }
    if (filter.contact) {
        filter.contact = [filter.contact]
    } else {
        filter.contact = ['0']
    }
    if (filter.entstatus) {
        filter.entstatus = [filter.entstatus]
    } else {
        filter.entstatus = ['0']
    }
    if (filter.registercapital) {
        filter.registercapital = [filter.registercapital]
    } else {
        filter.registercapital = ['0']
    }

    if ((zoomLevel.value <= 3 || obj.scope === 'companyname') && model !== 'range') {
        obj.filter.circle = null
    }


    return obj

})

let tm = null

const formatNumber = (num, type) => {
    num = Number(num)
    if (num === 0) {
        return num + ''
    } else if (type === 'distance') {
        return (num / 1000).toFixed(2)
    } else {
        if (num > 1 && num < 10000) {
            return num + ''
        } else {
            return (num / 10000).toFixed(2) + '万'
        }
    }
}
const getLxyData = () => {
    // 开始调用接口获取数据
    if (tm) {
        clearTimeout(tm)
    }
    tm = setTimeout(() => {
        let model = map.value.model
        let data = JSON.parse(JSON.stringify(searchData.value))

        data = { ...data, ...data.filter }

        delete data.filter
        console.log('data', data)

        if (zoomLevel.value === 4 || searchParams.keyword || model === 'range') {
            //区级调用公司接口

            aicService.searchCenterEnt(data).then((res) => {

                console.log(res)
                let list = res.data

                map.value.addMarkers(list.filter(lo => { return lo.lat }))

                let centerInfo = null
                if (model === 'range') {

                    centerInfo = map.value.rangeCenter
                } else {
                    centerInfo = window.map.getCenter()
                }
                if (!centerInfo) {
                    return
                }
                let center = new window.AMap.LngLat(centerInfo.lng, centerInfo.lat)
                list.forEach((item) => {
                    if (item.lon && item.lat) {
                        let p1 = [parseFloat(item.lon), parseFloat(item.lat)]
                        let distance = Math.round(window.AMap.GeometryUtil.distance(center, p1))
                        item.toCenterDistance = formatNumber(distance, 'distance')
                    }
                })

                companyList.value = list

                // this.listResult = list;
                listTotal.value = res.total
            })
        } else {
            // 其余调用统计接口
            aicService.searchStatistics(data).then(res => {
                console.log(res)
                map.value.addCircle(res.data)
            })
        }
    }, 200)
}

const scopeChange = (flag: string) => {
    searchParams.scope = flag
    searchParams.keyword = ''
    let lo = searchParams.filter.location
    searchParams.filter.location = lo.slice(0, 2)
}

let cacheData = []
const getCompanyName = async (query, callback) => {
    autoCompleteCallback.value = callback
    let data = JSON.parse(JSON.stringify(searchData.value))
    data = { ...data, ...data.filter }
    delete data.filter

    console.log('getCompanyName', data)
    let res = await aicService.searchCenterEnt(data)
    console.log(res)
    let arr = res.data.map((item) => {
        return { ...item, value: item.name }
    })
    cacheData = arr
    if (callback) {
        callback(arr)
        return
    }
}

const getMapAddress = (query, callback) => {
    autoCompleteCallback.value = callback
    window.map.plugin(['AMap.AutoComplete'], () => {
        var autoOptions = { city: searchParams.filter.location[0] }
        var autoComplete = new window.AMap.AutoComplete(autoOptions)
        autoComplete.search(query, (status, res) => {
            console.log(status, res)
            if (status === 'complete') {
                console.log('res.tips', res.tips)

                let arr = res.tips.map((item) => {
                    return { ...item, value: item.name, lat: item.location.lat, lon: item.location.lng }
                })
                cacheData = arr
                callback(arr)
            } else {
                callback([])
            }
        })
    })
}

const handleSelect = (e) => {
    console.log('eee', e)
    //将地图移动到点选的位置
    searchParams.keyword = e.value
    window.map.setCenter(new window.AMap.LngLat(e.lon, e.lat), true)
    map.value.getCenter()
    if (searchParams.scope === 'companyname') {
        window.map.setZoom(11)
        getLxyData()
        enterpriseMapInfoList.value.showDetail(e)
    } else {
        window.map.setZoom(15)
    }
}

const choseLoaction = (val) => {
    let list = cityRef.value.getCheckedNodes()[0].pathLabels
    console.log('list', list)
    let str = list[0] + '省'
    if (list[1]) {
        str += list[1] + '市'
    }
    if (list[2]) {
        str += list[2] + '区'
    }

    window.map.plugin(['AMap.AutoComplete'], () => {
        var autoOptions = { city: searchParams.filter.location[0] }
        var autoComplete = new window.AMap.AutoComplete(autoOptions)
        autoComplete.search(str, (status, res) => {
            console.log('status', status)
            if (status === 'complete') {
                console.log('res.tips', res.tips)

                let arr = res.tips.map((item) => {
                    return { ...item, value: item.name, lat: item.location.lat, lon: item.location.lng }
                })
                let e = arr[0]
                console.log('e', e)
                window.map.setCenter(new window.AMap.LngLat(e.lon, e.lat))
                if (val.length === 3) {
                    window.map.setZoom(13)
                } else if (val.length === 2) {
                    window.map.setZoom(12)
                } else {
                    window.map.setZoom(7)
                }
            }
        })
    }, () => {
        console.log('err')
    })
}

let autoCompleteCallback: Ref<null> = ref(null)
const querySearch = async (query, callback) => {
    if (tm) {
        clearTimeout(tm)
    }
    tm = setTimeout(() => {
        // this.$refs['list'].$data.page = 1;
        searchParams.page = 1
        if (searchParams.scope === 'companyname') {
            //公司名称搜索
            searchParams.filter.location = searchParams.filter.location.map(lo => { return lo.slice(0, 4 + '00') })
            getCompanyName(query, callback)
        } else {
            //地址搜索，调用地图接口
            getMapAddress(query, callback)
        }
    }, 200)
}

const changeAll = (val: number[]) => {
    let lastVal = val[val.length - 1]

    if (lastVal === 0) {
        searchParams.filter.enttype = [0]
    } else {
        searchParams.filter.enttype = searchParams.filter.enttype.filter((v) => { return v !== 0 })
    }
}

const getTransferList = async (pagesize: number) => {
    searchParams.pageSize = pagesize

    let data = JSON.parse(JSON.stringify(searchData.value))
    data = { ...data, ...data.filter }
    delete data.filter
    aicService.searchCenterEnt(data).then(res => {
        searchParams.pageSize = 20
        console.log('getTransferList===', res)
        if (res.data.length) {
            multipleSelection.value = res.data

            showBatchTransfer.value = true
        } else {
            ElMessage({
                type: 'error',
                message: '暂无要转移的线索',
            })
        }

    })
}

const transferCrmSuccess = () => {
    enterpriseMapInfoList.value.clearCheck()
    ElMessage({
        type: 'success',
        message: '转移成功',
    })
}

const pageChange = (page: number) => {
    searchParams.page = page
    getLxyData()
}

const transferCheck = (list: ICompanyInfo[]) => {
    if (!list || !list.length) {
        ElMessage({
            type: 'error',
            message: '请选择要转移的线索',
        })
    }
    multipleSelection.value = list
    showBatchTransfer.value = true
}

const querySearchRef = ref()
const handleQuerySearch = () => {

    console.log(autoCompleteCallback.value)
    if (autoCompleteCallback.value) {
        autoCompleteCallback.value(cacheData)
    }
}


onMounted(() => {
})

</script>

<style lang='scss' scoped>
.search-bar {
    background-color: var(--main-white);


    .search-bar-item {
        border-right: 1px solid var(--border-color);
        max-width: 300px;
        min-width: 150px;
        border-bottom: 1px solid var(--border-color);
    }

    :deep(.el-input__wrapper) {
        box-shadow: none !important;
    }

    :deep(.el-input__wrapper .is-focus) {
        box-shadow: none !important;
    }

    :deep(.el-select__wrapper) {
        box-shadow: none !important;
    }



}
</style>