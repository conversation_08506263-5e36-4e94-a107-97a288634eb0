<script lang="ts" setup>
import { computed, ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { UploadInstance, UploadProps } from 'element-plus'
type FromValue = 'leadPool' | 'leadList' | 'customerPool' | 'customerList'
const props = defineProps<{
    from: FromValue
}>()
const dialogVisible = ref(false)
const token = localStorage.getItem('access_token')
const headers = {
    'Shu-Auth': token ? `Bearer ${JSON.parse(token)}` : ''
}
const actionUrl = computed(() => {
    return `/api/zhenqi-crm/crm/import/${props.from === 'leadList' ? 1 : 2}`
})
const uploadRef = ref<UploadInstance>()
const uploadData = ref(
    {
        type:props.from === 'leadList' ? '1' : '2'
    }
)
const handleOpenImport = () => {
    dialogVisible.value = true
}
const FileList = ref<File[]>([]) // 保存原始文件对象

const beforeUpload: UploadProps['beforeUpload'] = (rawFile) => {
    if (rawFile.size / 1024 / 1024 > 124) {
        ElMessage.error('上传文件大小不能超过 124MB!')
        return false
    } else {
        return true
    }
}

type ImportResType = {
    errCode: number,
    errMsg: string,
    success: boolean,
    data?: object
}
const doUploadExcl = () => {
    if (!FileList.value.length) {
        ElMessage.warning('您还没有选择要导入的文件')
        return
    }
    // 增加扣费提示框
    ElMessageBox.confirm('导入将会扣除对应线索权益额度，是否确定使用？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type:'warning'
    }).then(() => {
        uploadRef.value?.submit()
    }).catch(() => {
        console.log('点击了取消')
    })
}
const emit = defineEmits(['refreshData'])
const uploadSuccess = (res: ImportResType) => {
    if (res.errCode === 0) {
        ElMessage.success('您的导入任务已创建，请到任务管理页面查看下载')
        dialogVisible.value = false
        handleClose()
        emit('refreshData')
    } else {
        ElMessage.error(res.errMsg)
        handleClose()
    }
}
const handleDownMould = () => {
    if (props.from === 'leadList') {
        window.open('https://zhenqi-cloud-bizs.oss-cn-shanghai.aliyuncs.com/template/线索导入模板.xls')
        // window.open('https://zhenqi-cloud-bizs.oss-cn-shanghai.aliyuncs.com/template/%E7%BA%BF%E7%B4%A2%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xls')
    } else if (props.from === 'customerList') {
        window.open('https://zhenqi-cloud-bizs.oss-cn-shanghai.aliyuncs.com/template/客户导入模板.xls')
        // window.open('https://zhenqi-cloud-bizs.oss-cn-shanghai.aliyuncs.com/template/%E5%AE%A2%E6%88%B7%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xls')
    }
}

const handleClose = () => {
    FileList.value = []
}
</script>
<template>
    <el-button class="color-black" @click="handleOpenImport()">导入</el-button>
    <el-dialog
        v-model="dialogVisible"
        title="导入文件"
        width="600"
        show-close
        destroy-on-close
        style="padding: 16px 24px"
        @close="handleClose()"
    >
        <el-upload
            ref="uploadRef"
            class="upload-demo"
            drag
            v-model:file-list="FileList"
            :auto-upload="false"
            accept=".xlsx,.xls"
            :headers="headers"
            :action="actionUrl"
            :limit="1"
            :data="uploadData"
            :before-upload="beforeUpload"
            :on-success="uploadSuccess"
        >
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
            <div class="color-black font-12">将文件拖到此处，<em class="!color-blue">点击上传</em></div>
            <template #tip>
                <div class="display-flex space-between font-12 ">
                    <div class="el-upload__tip">只能上传标准xlsx、xls模板文件</div>
                    <div class="el-upload__tip" style="color: var(--main-blue-); cursor: pointer" @click="handleDownMould()">下载模板</div>
                </div>
                <div style="color: #DB624F; font-size: 12px; margin-top: 4px;">单次限制导入300家</div>
                <div class="t-margin-8 font-14">提示：批量导入将扣除线索权益（每家企业仅在首次导入时扣减一次线索权益，后续重复导入不再扣减）。</div>
            </template>
        </el-upload>
        <div class="t-margin-16 display-flex">
            <el-button type="primary" style="margin: auto" @click="doUploadExcl()">导入</el-button>
        </div>
    </el-dialog>
</template>
<style scoped lang="scss">
.upload-demo{
    :deep(.el-upload-dragger){
        background-color: #f6f8fa;
    }
}
</style>
