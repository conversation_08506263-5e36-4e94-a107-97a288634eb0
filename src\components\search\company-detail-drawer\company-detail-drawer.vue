<template>

    <el-drawer v-model="drawerFlag" title="企业详情" size="80%" @close="handleClose">
        <SearchCompanyDetail @refreshList="refreshList" />
    </el-drawer>

</template>

<script lang='ts' setup>
import { ref, onMounted, provide, defineProps, defineEmits } from 'vue'
import type { Ref } from 'vue'
import SearchCompanyDetail from '@/components/search/company-detail/SearchCompanyDetail.vue'


const props = defineProps({
    socialCreditCode: {
        type: String,
        default: ''
    },
    drawer: {
        type: Boolean,
        default: false
    }
})

const emits = defineEmits(['update:drawer', 'refreshList'])

const refreshList = () => {
    emits('refreshList')
}
const handleClose = () => {
    emits('update:drawer', false)
}

const drawerFlag: Ref<boolean> = ref(props.drawer)

const socialCreditCode: Ref<string> = ref(props.socialCreditCode)
provide('socialCreditCode', socialCreditCode)
onMounted(() => {
})

</script>

<style lang='scss' scoped></style>