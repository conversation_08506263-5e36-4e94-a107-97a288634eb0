<template>
    <div>
        <div v-if="isLock" class="no-pay-item display-flex width-100 border-radius-8 oh" style="height: 400px">
            <div style="margin: auto" class="display-flex flex-column">
                <div style="text-align: center; margin-bottom: 5px">
                    <el-icon size="50" color="#409eff">
                        <Lock />
                    </el-icon>
                </div>
                <el-button size="large" type="primary" @click="pullLock">
                    点击查看
                </el-button>
            </div>
        </div>
        <div v-else>
            <div class="margin-top-30">
                <fm-generate-form v-if="modelItem" :data="jsonModelCurrentVersion" :print-read="true"
                                  ref="generateForm">
                    <template v-slot:legalperson="scope">
                        <div v-show="scope.model.channelType === 1">
                            <span class="pointer" v-if="scope.model.personRelatedEntNum > 1" style="color: #509de5"
                                  @click="openRelate(scope.model)">{{ scope.model.legalperson
                                  }}【关联{{ scope.model.personRelatedEntNum }}家企业】</span>
                            <span v-else>{{ scope.model.legalperson }}</span>
                        </div>
                        <div v-show="scope.model.channelType === 2">
                            <span class="pointer" style="color: #509de5" @click="openRelate(scope.model)">{{
                                scope.model.legalperson
                            }}</span>
                        </div>
                    </template>
                </fm-generate-form>
            </div>

            <!-- 关联关系 -->
            <relateDialog v-if='relateCompanyVisible' v-model:dialogVisable="relateCompanyVisible"
                          :relateInfo="relateInfo" />
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, defineProps, inject, watch, defineEmits } from 'vue'
import type { Ref } from 'vue'

import aicService from '@/service/aicService'
import { ElMessageBox, ElMessage } from 'element-plus'
import type { IGetModelCategoryResponse } from '@/types/company'

import orderService from '@/service/orderService'
import relateDialog from './custom-components/RelateDialog.vue'

const emits = defineEmits(['updateBuyStatus'])
interface definetypes {
    modelItem: IGetModelCategoryResponse,
    companyRelateItem: IGetModelCategoryResponse,
    isShowTitle?: boolean,
    outModelData?: Record<string, number | symbol | unknown | string | undefined>[] | null | []
}

const props = withDefaults(defineProps<definetypes>(), {
    isShowTitle: true,
    outModelData: null
})


const jsonModelCurrentVersion = ref({})
if (props.modelItem) {
    // console.log('props.modelItem', props.modelItem)
    jsonModelCurrentVersion.value = JSON.parse(props.modelItem.currentVersion.jsonStr)
    console.log('form', jsonModelCurrentVersion.value)
}

const isLock: Ref<boolean> = ref(false)

const socialCreditCode: Ref<string> = inject('socialCreditCode', ref(''))

const companyName: Ref<string> = inject('companyName', ref(''))
const crmBuyStatus = inject('crmBuyStatus')

watch(() => crmBuyStatus, (nVal) => {
    if (nVal === true) {
        isLock.value = true
    }
})

//获取模块数据

// const modelData = ref({})

const generateForm = ref<{ setData: <T>(data: T) => void } | null>(null)

const getModelData = () => {
    if (!props.modelItem?.name) {
        console.error('modelItem is undefined')
        return
    }
    aicService
        .gsInfo({
            socialCreditCode: socialCreditCode.value,
            modelName: props.modelItem.name,
        })
        .then((res) => {
            isLock.value = res.isLock === 1 ? true : false
            if (generateForm.value) {
                console.log('normalFrom', res)
                let obj = {}
                Object.keys(res).forEach(key => {
                    let v = res[key]
                    if (Array.isArray(v) && !v.length) {
                        v = ''
                    }
                    if (v === '') {
                        v = '-'
                    }
                    if (Array.isArray(v) && v.length) {
                        v = v.join(',')
                    }
                    obj[key] = v
                })
                generateForm.value.setData(obj)
            }
        })
}

const relateCompanyVisible: Ref<boolean> = ref(false)


interface IRelateInfo {
    pid?: string, legalperson: string
}
const relateInfo: Ref<IRelateInfo> = ref({} as IRelateInfo)




const openRelate = (val: IRelateInfo) => {
    relateInfo.value = val
    relateCompanyVisible.value = true
}


const pullLock = () => {
    ElMessageBox.confirm('扣除对应线索权益额度，是否确定使用?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(() => {
            orderService.orderBuyLegal({
                socialCreditCode: socialCreditCode.value,
                companyName: companyName.value,
                serviceKey: 'xs'
            }).then(() => {
                isLock.value = false


                emits('updateBuyStatus', true)
                ElMessage({
                    message: '使用成功',
                    type: 'success',
                })
            })
        })
        .catch(() => { })
}


onMounted(() => {
    if (!props.outModelData) {
        getModelData()
    } else {
        if (generateForm.value && props.modelItem && props.modelItem.innerModel && props.outModelData) {
            // console.log('mergeFrom', props.outModelData[props.modelItem.innerModel])
            // console.log('generateForm.value1', generateForm.value)
            generateForm.value.setData(props.outModelData[props.modelItem.innerModel])
            console.log('generateForm.value2', generateForm.value)
        }
    }

})
</script>

<style lang="scss" scoped>
.tab-text {
    font-size: 0.17rem;
    color: #909399;
}

.active {
    font-size: 0.2rem;
    color: #333;
    font-weight: bold;
}

.no-pay-item {
    background: url('@/assets/images/model-lock.jpg') no-repeat;
    width: 100%;
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    border: 3px solid var(--border-color);
}

.background-color-blue {
    background-color: var(--main-blue-);
}

.fm-report-table__td {
    background-color: #fff !important;
}
</style>