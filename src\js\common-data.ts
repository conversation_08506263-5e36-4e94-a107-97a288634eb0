export default {
    downloadUrl: {
        improtOrg: '/download/组织导入模板.xlsx',
        improtCompany: '/download/企业导入模板.xlsx',
        improtLead: '/download/线索导入模板.xls',
        improtCustomer: '/download/客户导入模板.xls',
        importOrder: '/order/对账单.xlsx',
    },
    modelCardList: [
        {
            icon: '/private-images/risk-type-icon/enforced.png',
            name: '被执行人',
            caregoryId: '',
            modelId: 'a841a09e633410f50017b4ab7fcf3043',
        },
        {
            icon: '/private-images/risk-type-icon/referee-documents.png',
            name: '裁判文书',
            caregoryId: '',
            modelId: 'a841a09e633410f50017b4ac767e8ab5',
        },
        {
            icon: '/private-images/risk-type-icon/court-notice.png',
            name: '法院公告',
            caregoryId: '',
            modelId: 'a841a09e633410f50017b4ad06abca09',
        },
        {
            icon: '/private-images/risk-type-icon/hearing-announcement.png',
            name: '开庭公告',
            caregoryId: '',
            modelId: 'a841a09e633410f50017b4ae79681457',
        },
        {
            icon: '/private-images/risk-type-icon/file-information.png',
            name: '立案信息',
            caregoryId: '',
            modelId: 'a841a09e633410f50017b4af51a32ca7',
        },
        {
            icon: '/private-images/risk-type-icon/bankruptcy-proceedings.png',
            name: '破产程序',
            caregoryId: '',
            modelId: 'a841a09e633410f50017b4b01baac9aa',
        },
        {
            icon: '/private-images/risk-type-icon/dishonest-people.png',
            name: '失信人',
            caregoryId: '',
            modelId: 'a841a09e633410f50017b4b12117c377',
        },
        {
            icon: '/private-images/risk-type-icon/judicial-case.png',
            name: '司法案件',
            caregoryId: '',
            modelId: 'a841a09e633410f50017b4b27e250f84',
        },
        {
            icon: '/private-images/risk-type-icon/freeze.png',
            name: '冻结拍卖',
            caregoryId: '',
            modelId: 'a841a09e633410f50017b4b3223927ab',
        },
        {
            icon: '/private-images/risk-type-icon/delivery-notice.png',
            name: '送达公告',
            caregoryId: '',
            modelId: 'a841a09e633410f50017b4b4736219bb',
        },
        {
            icon: '/private-images/risk-type-icon/limit-consumption.png',
            name: '限制高消费',
            caregoryId: '',
            modelId: 'a841a09e633410f50017b4b53f0dcb65',
        },
        {
            icon: '/private-images/risk-type-icon/end-case.png',
            name: '终本案件',
            caregoryId: '',
            modelId: 'a841a09e633410f50017b4b648d93fbd',
        },
        {
            icon: '/private-images/risk-type-icon/production-safety.png',
            name: '安全生产',
            caregoryId: '',
            modelId: 'a841a09e633410f50017b4b76c048532',
        },
        {
            icon: '/private-images/risk-type-icon/disciplinary-action.png',
            name: '纪律处分',
            caregoryId: '',
            modelId: 'a841a09e633410f50017b4b8755fbe5d',
        },
        {
            icon: '/private-images/risk-type-icon/public-safety.png',
            name: '公共安全',
            caregoryId: '',
            modelId: 'a841a09e633410f50017b4b91a850599',
        },
        {
            icon: '/private-images/risk-type-icon/environmental-law.png',
            name: '环境违法',
            caregoryId: '',
            modelId: 'a841a09e633410f50017b4ba3d77c9cb',
        },
        {
            icon: '/private-images/risk-type-icon/abnormal.png',
            name: '监管措施',
            caregoryId: '',
            modelId: 'a841a09e633410f50017b4bb33262cf1',
        },
        {
            icon: '/private-images/risk-type-icon/cancellation.png',
            name: '劳动纠纷',
            caregoryId: '',
            modelId: 'a841a09e633410f50017b4bc28a4739d',
        },
        {
            icon: '/private-images/risk-type-icon/abnormal-operation.png',
            name: '经营异常',
            caregoryId: '',
            modelId: 'a841a09e633410f50017b4bd2109c94e',
        },
        {
            icon: '/private-images/risk-type-icon/tax-illegal.png',
            name: '税收违法',
            caregoryId: '',
            modelId: 'a841a09e633410f50017b4be2a2a448f',
        },
        {
            icon: '/private-images/risk-type-icon/market-regulation.png',
            name: '市场监管',
            caregoryId: '',
            modelId: 'a841a09e633410f50017b4bf4cbc18f8',
        },
        {
            icon: '/private-images/risk-type-icon/engineering.png',
            name: '违设工程',
            caregoryId: '',
            modelId: 'a841a09e633410f50017b4c02917e30b',
        },
        {
            icon: '/private-images/risk-type-icon/punish.png',
            name: '行政处罚',
            caregoryId: '',
            modelId: 'a841a09e633410f50017b4c17aa7b9c8',
        },
        {
            icon: '/private-images/risk-type-icon/bond-defaults.png',
            name: '债券违约',
            caregoryId: '',
            modelId: 'a841a09e633410f50017b4c21af21ac7',
        },

        {
            icon: '/private-images/risk-type-icon/sentence.png',
            name: '证券交易',
            caregoryId: '',
            modelId: 'a841a09e633410f50017b4c3362483b4',
        },
        {
            icon: '/private-images/risk-type-icon/approval.png',
            name: '知识产权',
            caregoryId: '',
            modelId: 'a841a09e633410f50017b4c403acdc21',
        },
        {
            icon: '/private-images/risk-type-icon/message.png',
            name: '资金违规',
            caregoryId: '',
            modelId: 'a841a09e633410f50017b4c51c779fdc',
        },
        {
            icon: '/private-images/risk-type-icon/criminal.png',
            name: '刑事强制',
            caregoryId: '',
            modelId: 'a841a09e633410f50017b4c66e654201',
        },
    ],
    commonFunctionMenu: [
        {
            label: '风险列表',
            url: '../../static/common-menu/risk-list.png',
            router: '/pages/company-manage/risk-manage',
        },
        {
            label: '风险监控',
            url: '../../static/common-menu/risk-overview.png',
            router: '/pages/company-manage/risk-monitor',
        },
        {
            label: '线索池',
            url: '../../static/common-menu/lead-pool.png',
            router: '/pages/su-crm/lead-pool-list',
        },
        {
            label: '收支明细',
            url: '../../static/common-menu/mall-order.png',
            router: '/pages/su-added-service/my-order',
        },
        {
            label: '我的线索',
            url: '../../static/common-menu/my-lead.png',
            router: '/pages/su-crm/lead-list',
        },
        {
            label: '客户列表',
            url: '../../static/common-menu/customer-list.png',
            router: '/pages/su-crm/customer-list',
        },
    ],
    timePickerShortcuts: [
        {
            text: '3月内',
            value: () => {
                const end = new Date()
                const start = new Date()
                start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
                return [start, end]
            },
        },
        {
            text: '半年内',
            value: () => {
                const end = new Date()
                const start = new Date()
                start.setTime(start.getTime() - 3600 * 1000 * 24 * 182)
                return [start, end]
            },
        },
        {
            text: '1年内',
            value: () => {
                const end = new Date()
                const start = new Date()
                start.setTime(start.getTime() - 3600 * 1000 * 24 * 365)
                return [start, end]
            },
        },
        {
            text: '1年 - 3年',
            value: () => {
                const end = new Date()
                const start = new Date()
                start.setTime(start.getTime() - 3600 * 1000 * 24 * 1095)
                end.setTime(end.getTime() - 3600 * 1000 * 24 * 365)
                return [start, end]
            },
        },
        {
            text: '3年 - 5年',
            value: () => {
                const end = new Date()
                const start = new Date()
                start.setTime(start.getTime() - 3600 * 1000 * 24 * 1825)
                end.setTime(end.getTime() - 3600 * 1000 * 24 * 1095)
                return [start, end]
            },
        },
        {
            text: '5年 - 10年',
            value: () => {
                const end = new Date()
                const start = new Date()
                start.setTime(start.getTime() - 3600 * 1000 * 24 * 1825)
                end.setTime(end.getTime() - 3600 * 1000 * 24 * 3650)
                return [start, end]
            },
        },
        {
            text: '10年以上',
            value: () => {
                const end = new Date()
                const start = new Date()
                start.setTime(26933000)
                end.setTime(end.getTime() - 3600 * 1000 * 24 * 3650)
                return [start, end]
            },
        },
    ],
    searchTagTypes: [
        {
            label: '智能搜索',
            val: '0',
            plac: '智能搜索，输入任意关键词，以空格隔开',
            searchKeys: [
                { key: 'businessscope', title: '经营范围' },
                { key: 'history_name_ws', title: '历史名称' },
                { key: 'b2bInfo', title: '企业简介' },
                { key: 'baike', title: '百科简介' },
                { key: 'auction', title: '招标投标' },
                { key: 'brandName_ws', title: '品牌信息' },
                { key: 'products', title: '产品' },
            ],
        },
        {
            label: '企业名称',
            val: 'companyname',
            plac: '请输入企业名称',
            searchKey: 'history_name_ws',
            searchTitle: '历史名称',
        },
        {
            label: '网络推广',
            val: 'semkeyword',
            plac: '请输入网络推广关键词或产品名称',
            searchKey: 'semkeywords',
            searchTitle: '推广关键词',
        },
        {
            label: '经营范围',
            val: 'businessscope',
            plac: '请输入主营业务或经营范围关键词',
            searchTitle: '经营范围',
            searchKey: 'businessscope',
        },
        {
            label: '相关人员',
            val: 'personnel',
            plac: '请输入一个完整姓名（支持企业法人/高管/其他员工）',
            searchTitle: '相关人员',
            searchKey: 'personnel_info',
        },
        {
            label: '联系方式',
            val: 'contactWay',
            plac: '请输入联系方式，包括手机号（请输入11位完整手机号）、固话、邮箱',
            searchKeys: [
                { key: 'email', title: '邮箱' },
                { key: 'mobilePhone', title: '手机' },
            ],
        },
        {
            label: '招聘职位',
            val: 'jobtitle',
            plac: '请输入招聘职位（支持搜索最近6个月内的职位）',
            searchKey: 'jobtitle',
            searchTitle: '招聘职位',
        },
        {
            label: '统一码',
            val: 'unicode',
            plac: '请输入统一社会信用代码、工商注册号',
            searchKey: 'uncid',
            searchTitle: '统一码',
        },
        {
            label: '地址',
            val: 'companyaddress',
            plac: '请输入地址，支持搜索通讯地址和注册地址',
            searchKey: 'regAddress',
            searchTitle: '注册地址',
        },
        {
            label: '产品',
            val: 'products',
            plac: '请输入产品、APP名称、招投标',
            searchTitle: '产品',
            searchKey: 'products',
        },


    ],
    lxySceneSearchType: [
        {
            label: '搜招标投标',
            name: 'tenderProjectSearch',
            apiHost: 'tender_project_search',
            desc: '搜有招标投标信息企业',
            icon: '../../static/search-company/search-ztb.png',
            plac: '请输入关键词',
            child: [
                {
                    label: '招标投标项目查询',
                    name: 'tenderProjectSearch',
                    apiHost: 'tender_project_search',
                },
                {
                    label: '企业查询',
                    name: 'searchProject',
                    apiHost: 'scene_search',
                },
            ],
        },
        {
            label: '搜工厂',
            name: 'searchFactory',
            apiHost: 'scene_search',
            desc: '搜生产加工类企业',
            icon: '../../static/search-company/search-gc.png',
            plac: '请输入企业名称或者主营产品',
        },
    ],
    leadStatusEnum: [
        { label: '未处理', value: 1 },
        { label: '联系方式有效', value: 2 },
        { label: '联系方式无效', value: 3 },
        { label: '关闭', value: 4 },
    ],
    customerStatusEnum: [
        { label: '初访', value: 1, icon: 'icon-a-huaban270' },
        { label: '意向', value: 2, icon: 'icon-a-huaban271' },
        { label: '报价', value: 3, icon: 'icon-a-huaban881' },
        { label: '搁置', value: 5, icon: 'icon-a-huaban891' },
        { label: '成交', value: 4, icon: 'icon-a-huaban901' },
    ],
    taskList: [
        { label: '测试外呼任务', value: 1 },
        { label: '测试外呼任务2', value: 2 },
    ],
    weekRange: [
        { label: '周一', value: 1 },
        { label: '周二', value: 2 },
        { label: '周三', value: 3 },
        { label: '周四', value: 4 },
        { label: '周五', value: 5 },
        { label: '周六', value: 6 },
        { label: '周日', value: 7 },
    ],
    aiSkills: [
        { label: '话术名称', value: 1 },
        { label: '话术名称2', value: 2 },
    ],
    policyList: [
        { label: '政策名称', value: 1 },
        { label: '政策名称2', value: 2 },
    ],
    mapContactListEnum: [
        {
            value: '',
            label: '不限',
        },
        {
            value: '1',
            label: '有手机',
        },
        {
            value: '2',
            label: '有固话',
        },
        {
            value: '3',
            label: '有邮箱',
        },
        {
            value: '4',
            label: '有QQ',
        },
        {
            value: '5',
            label: '无联系方式',
        },
    ],
    mapStatusListEnum: [
        {
            value: '',
            label: '不限',
        },
        {
            value: '1',
            label: '在营/存续',
        },
        {
            value: '4',
            label: '迁入/迁出',
        },
        {
            value: '2',
            label: '吊销/撤销',
        },
        {
            value: '3',
            label: '注销',
        },
        {
            value: '8',
            label: '停业',
        },
        {
            value: '9',
            label: '其他',
        },
    ],
    mapCapitalListEnum: [
        {
            value: '',
            label: '不限',
        },
        {
            value: '1',
            label: '20万以下',
        },
        {
            value: '2',
            label: '20~50万',
        },
        {
            value: '3',
            label: '50~100万',
        },
        {
            value: '4',
            label: '100~200万',
        },
        {
            value: '5',
            label: '200~500万',
        },
        {
            value: '6',
            label: '500万以上',
        },
    ],
    mapEnterpriseTypeListEnum: [
        {
            value: 0,
            label: '全部类型',
        },
        {
            value: 1,
            label: '民营',
        },
        {
            value: 2,
            label: '国有企业',
        },
        {
            value: 3,
            label: '外资、中外合资',
        },
        {
            value: 4,
            label: '港、澳、台投资',
        },
        {
            value: 5,
            label: '工商个体户',
        },
        {
            value: 6,
            label: '其他企业',
        },
        {
            value: 7,
            label: '社会组织',
        },
    ],
    menuTypeList: [
        { label: '一级菜单', value: 1 },
        { label: '子菜单', value: 2 },
        { label: '权限', value: 3 },
    ],
    callStatusList: [
        {
            value: 99,
            label: '已接听',
        },
        {
            value: 1,
            label: '未拨打',
        },
        {
            value: 2,
            label: '等待接听',
        },
        {
            value: 3,
            label: '接听中',
        },
        {
            value: 4,
            label: '等待重呼',
        },
        {
            value: 5,
            label: '未接听',
        },
        {
            value: 6,
            label: '拨打失败',
        },

        {
            value: 8,
            label: '限制拨打',
        },
        {
            value: 9,
            label: '占线',
        },
        {
            value: 11,
            label: '来电提醒',
        },
        {
            value: 12,
            label: '无法接通',
        },
        {
            value: 13,
            label: '空号',
        },
        {
            value: 14,
            label: '停机',
        },
        {
            value: 15,
            label: '关机',
        },
        {
            value: 17,
            label: '号码故障',
        },
        {
            value: 18,
            label: '线路故障',
        },
    ],
    aiTagName: [
        {
            value: '有兴趣',
            label: '有兴趣',
        },
        {
            value: '没兴趣',
            label: '没兴趣',
        },
    ],
    recallStatusDicts: [
        { label: '关机', value: 15 },
        { label: '停机', value: 14 },
        { label: '占线', value: 9 },
        { label: '无法接通', value: 12 },
        { label: '未接', value: 5 },
        { label: '拨打失败', value: 6 },
        { label: '来电提醒', value: 11 },
    ],
    recallTimesDicts: [
        { label: '1次', value: 1 },
        { label: '2次', value: 2 },
        { label: '3次', value: 3 },
        { label: '4次', value: 4 },
        { label: '5次', value: 5 },
    ],
    companycallStatusList: [
        {
            value: 1,
            label: '呼叫中',
        },
        {
            value: 3,
            label: '成功',
        },
        {
            value: 2,
            label: '失败',
        },
    ],
    salesDynamicsTypes: [
        {
            value: 'turn',
            text: '新增',
        },
        {
            value: 'toUser',
            text: '转移',
        },
        {
            value: 'create',
            text: '跟进',
        },
        {
            value: 'update',
            text: '跟进状态',
        },
        {
            value: 'outbound',
            text: '智能外呼',
        },
        {
            value: 'other',
            text: '其他',
        },
    ],
    permissions: {
        searchBid: { name: '搜招投标', value: 'search_bid' },
        searchFactory: { name: '搜工厂', value: 'search_factory' },
        recentReg: { name: '最新注册', value: 'recent_reg' },
        leadDelete: { name: '线索池-删除', value: 'lead_delete' },
        leadlistDelete: { name: '线索列表-删除', value: 'leadlist_delete' },
        leadlistExport: { name: '线索列表-导出', value: 'leadlist_export' },
        leadlistImport: { name: '线索列表-导入', value: 'leadlist_import' },
        leadlistAdd: { name: '线索列表-新增', value: 'leadlist_add' },
        companypoolDelete: { name: '客户公海-删除', value: 'companypool_delete' },
        companyAll: { name: '全部客户', value: 'company_all' },
        companyDelete: { name: '客户列表-删除', value: 'company_delete' },
        companyExport: { name: '客户列表-导出', value: 'company_export' },
        companyImport: { name: '客户列表-导入', value: 'company_import' },
        companyAdd: { name: '客户列表-新增', value: 'company_add' },
        outboundtaskStart: { name: '开始任务', value: 'outboundtask_start' },
        outboundtaskEdit: { name: '编辑任务', value: 'outboundtask_edit' },
        outboundtaskPause: { name: '暂停任务', value: 'outboundtask_pause' },
        outboundtaskAbort: { name: '终止任务', value: 'outboundtask_abort' },
        outboundtaskDelete: { name: '删除任务', value: 'outboundtask_delete' },
        outboundtaskExport: { name: '外呼任务详情导出', value: 'outboundtask_export' },
        outboundExport: { name: '外呼客户-导出', value: 'outbound_export' },
        benefitsListExport: { name: '权益列表-导出', value: 'benefits-list-export' },
        benefitsUsageExport: { name: '消费明细-导出', value: 'benefits-usage-export' },
        collectionRecordsExport: { name: '采集记录-导出', value: 'collection-records-export' },
        userAdd: { name: '新增用户', value: 'user_add' },
        orgAdd: { name: '新增组织', value: 'org_add' },
        userEdit: { name: '编辑用户', value: 'user_edit' },
        orgEdit: { name: '编辑组织', value: 'org_edit' },
        orgDelete: { name: '删除组织', value: 'org_delete' },
        roleAdd: { name: '角色权限-新增', value: 'role_add' },
        roleEdit: { name: '角色权限-编辑', value: 'role_edit' },
        roleDelete: { name: '角色权限-删除', value: 'role_delete' },
        tagMenuAdd: { name: '企业标签-新增', value: 'tag_menu_add' },
        tagMenuDelete: { name: '企业标签-编辑', value: 'tag_menu_delete' },
        tagMenuEdit: { name: '企业标签-删除', value: 'tag_menu_edit' },
        businessAdd: { name: '业务配置-新增', value: 'business_add' },
        businessEdit: { name: '业务配置-编辑', value: 'business_edit' },
        businessDelete: { name: '业务配置-删除', value: 'business_delete' },
        leadpoolAdd: { name: '线索池-新增', value: 'leadpool_add' },
        leadpoolEdit: { name: '线索池-编辑', value: 'leadpool_edit' },
        leadpoolDelete: { name: '线索池-删除', value: 'leadpool_delete' },
        publicpoolAdd: { name: '公海-新增', value: 'publicpool_add' },
        publicpoolEdit: { name: '公海-编辑', value: 'publicpool_edit' },
        publicpoolDelete: { name: '公海-删除', value: 'publicpool_delete' },
        tenantAdd: { name: '租户管理-新增', value: 'tenant_add' },
        transferNewLead: { name: '转移新线索', value: 'transfer_new_lead' },
        contactView: { name: '查看联系方式', value: 'contact-view' },
        smsSend: { name: '发送短信', value: 'sms-send' },
        contactUpdate: { name: '更新联系方式', value: 'contact-update' },
        dialingTaskCreate: { name: '发起智能外呼', value: 'dialing-task-create' },
    },
    menus: {
        home: { name: '首页', id: '1908783032769429505' },
        search: { name: '查找企业', id: '1908783987225247746' },
        searchCompany: { name: '找企业', id: '1908788930027962369' },
        moreSearchCompany: { name: '高级搜索', id: '1908789066984570881' },
        companyMap: { name: '地图搜索', id: '1908789109623865346' },
        suCrm: { name: '客户线索', id: '1908784477111566338' },
        suCrmLeadPool: { name: '线索池', id: '1908789190028673025' },
        suCrmLeadList: { name: '线索列表', id: '1908789229924892673' },
        crmCustomerPool: { name: '客户公海', id: '1908789253824036866' },
        suCrmCustomerList: { name: '客户列表', id: '1908789268013367297' },
        companyManage: { name: '客户列表', id: '1908787972682268674' },
        riskMonitor: { name: '风险监控', id: '1908789317028003841' },
        riskManage: { name: '风险列表', id: '1908789327140470786' },
        dataCenter: { name: '数据中心', id: '1908788031125700610' },
        aiPhone: { name: '智能外呼', id: '1908788109777289218' },
        aiPhoneTask: { name: '外呼任务', id: '1908789387198709761' },
        aiPhoneCustomer: { name: '外呼客户', id: '1908789398686908418' },
        operateManage: { name: '运营管理', id: '1908788174214381570' },
        smsSettings: { name: '短信配置', id: '1908789467486076930' },
        policyManagement: { name: '短信配置', id: '1908789489371955202' },
        financialProducts: { name: '金融产品管理', id: '1908789501178920962' },
        advanceTemplate: { name: '高级搜索模板', id: '67f8c417966ebeb6c5eb7361' },
        projectClassification: { name: '项目分类', id: '1912717798739460098' },
        userOverview: { name: '权益中心', id: '1908788220536274945' },
        userPrivileges: { name: '用户权益', id: '1908789548008325122' },
        collectionRecords: { name: '采集记录', id: '1908789557655224321' },
        systemManagement: { name: '系统管理', id: '1908788264928788482' },
        mailList: { name: '内部组织管理', id: '1908789616379674625' },
        crmTag: { name: '企业标签', id: '1908789637497995266' },
        tenantSettings: { name: '业务配置', id: '1908789657169281025' },
        leadPoolManagement: { name: '线索池管理', id: '1908789701465325569' },
        customerPool: { name: '客户公海管理', id: '1908789722386513922' },
        taskManagement: { name: '任务管理', id: '1927331038987481090' },
        tenantManagement: { name: '租户管理', id: '1908789733698551810' },
        dataChannel: { name: '数据通道', id: '1910150246491619329' },
        menuManagement: { name: '菜单管理', id: '1920394107394699265' },
    },
    companyRecallStatusList: [
        {
            value: 1,
            label: '已重呼',
        },
        {
            value: 0,
            label: '未重呼',
        },
    ],
}
