<template>
    <el-drawer title="查看匹配企业" v-model="dialogTableVisible" size="750px" @close="close">
        <div class="height-100">
            <el-scrollbar v-if="matchCompanys.length">
                <div v-for="company in matchCompanys" :key="company.name"
                     class="border border-radius-8 all-padding-16 b-margin-12 pointer" @click="showDetail(company)">
                    <div class="display-flex">
                        <div class="flex-1 font-18">
                            {{ company.name }}
                        </div>
                        <div class="flex-1 text-right color-primary">
                            匹配度：{{ company.totalScore }}%
                        </div>
                    </div>
                    <div class="font-14 t-margin-4">
                        <span class="color-primary">{{ getMaxRules(company).length }}</span> 项符合申请条件
                    </div>
                    <div class="rule-back border-radius-4 t-margin-4 tb-padding-8 lr-padding-16"
                         v-if="getMaxRules(company).length">
                        <el-row :gutter="20">
                            <el-col class="display-flex top-bottom-center tb-margin-5 font-14" :span="8"
                                    v-for="rule in getMaxRules(company)" :key="rule.id">
                                <div class="display-flex">
                                    <div class="r-margin-4"> <el-icon :color="'#52C41A'">
                                        <CircleCheck />
                                    </el-icon></div>
                                    <div> {{ rule.propLabel }}</div>

                                </div>
                            </el-col>
                        </el-row>
                    </div>
                </div>
            </el-scrollbar>
            <el-empty description="暂无匹配企业" v-else></el-empty>
        </div>

        <CrmProductMatchDrawer v-if="drawerVisible && type === 'product'" v-model:visible="drawerVisible"
                               :detailInfo="productMatchItem" />
        <CrmPolicyMatchDrawer v-if="drawerVisible && type === 'policy'" v-model:visible="drawerVisible"
                              :detailInfo="policyMatchItem" />
    </el-drawer>

</template>

<script lang='ts' setup>
import { ref, onMounted, defineProps, defineEmits, defineExpose } from 'vue'
import type { Ref } from 'vue'

import type { IGoodsProductItem, IGoodsPolicyItem } from '@/types/lead'

import CrmProductMatchDrawer from '../crm/crm-product-match/CrmProductMatchDrawer.vue'
import CrmPolicyMatchDrawer from '../crm/crm-policy-match/CrmPolicyMatchDrawer.vue'

const props = defineProps({
    goodsId: {
        type: String,
        default: '',
        required: true
    },
    showDialog: {
        type: Boolean,
        default: false
    },
    type: {
        type: String,
        required: true
    }
})

const emits = defineEmits(['update:showDialog'])



import crmService from '@/service/crmService'


const dialogTableVisible: Ref<boolean> = ref(false)

const close = () => {
    emits('update:showDialog', false)
}

const matchCompanys: Ref<IGoodsProductItem[] | IGoodsPolicyItem[]> = ref([])
const seeMatchCompany = () => {
    if (props.type === 'policy') {
        crmService.crmGoodsPolicyRuleMatchEnt(props.goodsId).then(res => {
            dialogTableVisible.value = true
            matchCompanys.value = res
        })
    }

    if (props.type === 'product') {
        crmService.crmGoodsFinanceRuleMatchEnt(props.goodsId).then(res => {
            dialogTableVisible.value = true
            matchCompanys.value = res
        })

    }

}

const getMaxRules = (item: IGoodsProductItem | IGoodsPolicyItem) => {
    if (!item.flatRules) { return [] }
    return item.flatRules.filter(rule => {
        let key = `${rule.id}_${rule.prop}_score`
        return item[key] === rule.maxScore
    })
}

const drawerVisible: Ref<boolean> = ref(false)

const policyMatchItem: Ref<IGoodsPolicyItem> = ref({} as IGoodsPolicyItem)
const productMatchItem: Ref<IGoodsProductItem> = ref({} as IGoodsProductItem)

const showDetail = (item: IGoodsProductItem | IGoodsPolicyItem) => {
    if (props.type === 'policy') {
        policyMatchItem.value = item as IGoodsPolicyItem
    }
    if (props.type === 'product') {
        productMatchItem.value = item as IGoodsProductItem
    }

    drawerVisible.value = true
}

defineExpose({
    seeMatchCompany
})
onMounted(() => {
})

</script>

<style lang='scss' scoped>
.rule-back {
    background-color: rgba(25, 102, 255, .06);
}
</style>