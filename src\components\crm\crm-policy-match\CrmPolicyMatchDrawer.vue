<script lang="ts" setup>
import {
    ref,
    // inject,
    watch, getCurrentInstance, provide
} from 'vue'
// import { ElMessage, ElMessageBox } from 'element-plus'
// import crmService from '@/service/crmService'
// import type { Ref } from 'vue'
import type {
    // ILeadData, 
    IGoodsPolicyItem
} from '@/types/lead'

import MatchRules from '@/components/match-rules/Index.vue'
const instance = getCurrentInstance()
const moment = instance?.appContext.config.globalProperties.$moment
const props = defineProps<{
    visible: boolean,
    detailInfo: IGoodsPolicyItem,
    isDetail?: boolean
}>()

provide('isDetail', props.isDetail)
const drawerVisible = ref(props.visible)
watch(
    () => props.visible,
    (newVal) => {
        drawerVisible.value = newVal
    }
)
// const crmDetail: Ref<ILeadData> = inject('crmDetail') as Ref<ILeadData>

const emit = defineEmits(['update:visible'])
const handleClose = () => {
    emit('update:visible', false)
}
const tabList = [
    {
        id: 0,
        label: '项目简介',
    },
    {
        id: 1,
        label: '奖励措施',
    },
    {
        id: 2,
        label: '申报条件',
    },
    {
        id: 3,
        label: '申报截止时间',
    },
    {
        id: 4,
        label: '对比详情',
    },
]
const chooseItemId = ref(0)
const handleClickTab = (id: number) => {
    // 滚动到指定的元素
    chooseItemId.value = id
    const ele = document.querySelectorAll('.policy-tab-item')
    const targetEle = ele[id]
    targetEle.scrollIntoView({
        behavior: 'smooth',
    })
}

// const handleTurnFws = () => {

//     console.log('转移给服务商', crmDetail.value)
//     ElMessageBox.confirm('确认将该线索转换给服务商吗?', '提示', {
//         confirmButtonText: '确认',
//         cancelButtonText: '取消',
//         type: 'warning',
//     }).then(() => {
//         if (!props.detailInfo.id) {
//             return
//         }

//         crmService.goodsPolicyTransfer({
//             leadId: crmDetail.value.id,
//             productId: props.detailInfo.id
//         }).then(() => {
//             ElMessage({
//                 type: 'success',
//                 message: '操作成功'
//             })
//         })
//     }).catch(() => {
//         console.log('点击了取消')
//     })
// }
</script>
<template>
    <el-drawer :title="detailInfo.name" v-model="drawerVisible" v-if="drawerVisible" size="70%" @close="handleClose">
        <!-- <el-button type="primary" @click="handleTurnFws">转给服务商</el-button> -->
        <div class="item-tab t-margin-10">
            <div v-for="(item, index) in tabList" :key="index" class="item-tab-li pointer"
                 :class="{ active: chooseItemId === item.id }" @click="handleClickTab(item.id)">
                {{ item.label }}
            </div>
        </div>

        <!-- 产品大纲 -->
        <div class="policy-tab-item">
            <div class="display-flex top-bottom-center">
                <Icon class="r-margin-5" icon="icon-a-huaban274" :size="16" color="var(--main-blue-)" />
                <div>项目简介</div>
            </div>
            <div class=" lr-padding-24 tb-margin-16">
                <el-row :gutter="20">
                    <el-col :span="8" class="b-margin-12">
                        政策类型：{{ detailInfo.childGoodsTypeStr || '-' }}

                    </el-col>
                    <el-col :span="8" class="b-margin-12">
                        政策标题：{{ detailInfo.name || '-' }}

                    </el-col>
                    <el-col :span="8" class="b-margin-12">
                        政策文号：{{ detailInfo.spu?.policyNumber || '-' }}

                    </el-col>
                    <el-col :span="8" class="b-margin-12">
                        政策级别：{{ detailInfo.spu?.policyLevelStr || '-' }}

                    </el-col>
                    <el-col :span="8" class="b-margin-12">
                        行业类别：{{ detailInfo.spu?.policyIndustryStr || '-' }}

                    </el-col>
                    <el-col :span="8" class="b-margin-12">
                        发文部门：{{ detailInfo.spu?.issuingDepartmentStr || '-' }}

                    </el-col>
                    <el-col :span="8" class="b-margin-12">
                        主题分类：{{ detailInfo.spu?.policyTopicStr || '-' }}
                    </el-col>
                </el-row>
            </div>
        </div>

        <!-- 返佣说明 -->
        <div class="policy-tab-item">
            <div class="display-flex top-bottom-center">
                <Icon class="r-margin-5" icon="icon-a-huaban86" :size="16" color="var(--main-blue-)"></Icon>
                <div>奖励措施</div>
            </div>
            <div class="l-padding-24 color-three-grey font-16 tb-margin-16">
                {{ detailInfo.spu?.incentives || '-' }}
            </div>
        </div>

        <!-- 准入区域 -->
        <div class="policy-tab-item">
            <div class="display-flex top-bottom-center">
                <Icon class="r-margin-5" icon="icon-a-huaban2751" :size="16" color="var(--main-blue-)"></Icon>
                <div>申报条件</div>
            </div>
            <div class="lr-margin-24 font-16 tb-margin-16">
                {{ detailInfo.spu?.declareCondition || '-' }}
            </div>
        </div>

        <!-- 禁入行业 -->
        <div class="policy-tab-item">
            <div class="display-flex top-bottom-center">
                <Icon class="r-margin-5" icon="icon-a-huaban2761" :size="16" color="var(--main-blue-)"></Icon>
                <div>申报截止时间</div>
            </div>
            <div class="lr-margin-24 tb-margin-16 display-flex">
                截止日期：{{ detailInfo.spu?.deadlineTime ? moment(detailInfo.spu.deadlineTime).format("YYYY-MM-DD") : '-' }}
                <span v-if="detailInfo.spu?.deadlineTime">*
                    以政府当年发文为准</span>
            </div>
        </div>

        <!-- 对比详情 -->
        <div class="policy-tab-item">
            <div class="display-flex top-bottom-center b-margin-16">
                <Icon class="r-margin-5" icon="icon-a-huaban273" :size="16" color="var(--main-blue-)"></Icon>
                <div>对比详情</div>
            </div>
            <MatchRules v-if="detailInfo.rules" :read-only="true" :rules="detailInfo.rules.list" :scoreInfo="detailInfo.matchScore
            " />
        </div>
    </el-drawer>
</template>
<style scoped lang="scss">
.item-tab {
    width: 632px;
    height: 56px;
    border-radius: 8px;
    background: rgba(245, 247, 250, 1);
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 4px 4px 4px 4px;
    margin-bottom: 20px;

    .item-tab-li {
        min-width: 112px;
        height: 48px;
        border-radius: 4px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 12px 24px 12px 24px;

        &:hover {
            background: #fff;
            box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.04);
        }
    }
}

.active {
    background: #fff;
    box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.04);
}
</style>
