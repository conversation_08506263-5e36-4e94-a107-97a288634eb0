<template>
    <el-table ref="table" :data="crmDetail.releationLeadList" tooltip-effect="dark" border table-layout="fixed"
              :header-cell-style="{
                  background: '#ECF5FF',
              }" size="large" empty-text="暂无数据">

        <el-table-column label="名称" prop="name" />
        <el-table-column label="手机" prop="contactInfo.mobile" />
        <el-table-column label="线索来源" prop="sourceStr" />
        <el-table-column label="地址" prop="customFields.address" />
        <el-table-column label="负责人" prop="user" />


    </el-table>
</template>

<script lang='ts' setup>
import { onMounted, inject } from 'vue'
import type { Ref } from 'vue'
import type { ILeadData } from '@/types/lead'
// import type { Ref } from 'vue'
// import { useRoute } from 'vue-router'
const crmDetail: Ref<ILeadData> = inject('crmDetail') as Ref<ILeadData>
// const route = useRoute()
onMounted(() => { })
</script>

<style lang='scss' scoped></style>