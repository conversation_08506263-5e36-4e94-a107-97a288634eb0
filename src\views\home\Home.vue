<script lang="ts" setup>
import {
    AreaDataOverview,
    Gmys,
    Jszx,
    Kcdk,
    Kjxqy,
    Kjzz,
    Qyqyyt,
    Smzq,
    Ssrz,
    Zscq,
} from '@/views/home/<USER>/index'
import { computed, ref, onMounted } from 'vue'
import homeService from '@/service/homeService'
import type { ModelRes } from '@/types/home'
import type { RootState } from '@/types/store'
import { useStore } from 'vuex'
const store = useStore<RootState>()
const Model001Res = ref<ModelRes[]>([])
const Model003Res = ref<ModelRes[]>([])
const Model004Res = ref<ModelRes[]>([])
const Model005Res = ref<ModelRes[]>([])
const Model006Res = ref<ModelRes[]>([])
const Model007Res = ref<ModelRes[]>([])
const tenantInfo = computed(() => {
    const { account } = store.state.user || {}
    const { tenant } = account || {}
    return tenant
})
onMounted(() => {
    const checkTenant = () => {
        if (tenantInfo.value) {
            console.log('tenantInfo', JSON.stringify(tenantInfo.value))
            getData()
        } else {
            setTimeout(checkTenant, 10)
        }
    }
    checkTenant()
})

const getData = () => {
    homeService
        .searchHomeData({
            code: tenantInfo.value?.areaCode ? tenantInfo.value?.areaCode : '3201',
            roundName: '',
            industryCode: '',
            params: [],
        })
        .then((res) => {
            const { Model001, Model003, Model004, Model005, Model006, Model007 } = res
            if (Model001) Model001Res.value = Model001
            if (Model003) Model003Res.value = Model003
            if (Model004) Model004Res.value = Model004
            if (Model005) Model005Res.value = Model005
            if (Model006) Model006Res.value = Model006
            if (Model007) Model007Res.value = Model007
            console.log('首页数据', res)
        })
}
// onBeforeMount(() => {
//     getData()
// })
</script>

<template>
    <div class="dashboard">
        <div class="display-flex space-between" style="margin-bottom: 16px">
            <div class="top-left">
                <!-- XX市数据总览 -->
                <div style="padding-bottom: 16px">
                    <AreaDataOverview :data="Model001Res" :tenantInfo="tenantInfo"></AreaDataOverview>
                </div>
                <!-- 规模以上企业 (暂无数据)-->
                <div>
                    <Gmys></Gmys>
                </div>
            </div>
            <!-- 上市融资企业 -->
            <div class="top-right">
                <Ssrz></Ssrz>
            </div>
        </div>
        <div class="display-flex space-between" style="margin-bottom: 16px">
            <div class="contain-left" style="width: 65%">
                <div class="display-flex space-between" style="margin-bottom: 16px">
                    <Zscq style="margin-right: 16px" :data="Model004Res"></Zscq>
                    <Smzq></Smzq>
                </div>
                <div class="display-flex space-between">
                    <div style="width: 60%; margin-right: 10px">
                        <Jszx style="margin-bottom: 16px" :data="Model006Res"></Jszx>
                        <Kcdk></Kcdk>
                    </div>
                    <div style="width: 30%" class="flex-1">
                        <Kjxqy :data="Model003Res"></Kjxqy>
                    </div>
                </div>
            </div>
            <div class="contain-right" style="width: 34%">
                <Kjzz :data="Model005Res" :tenantInfo="tenantInfo"></Kjzz>
            </div>
        </div>
        <div style="width: 100%">
            <Qyqyyt :data="Model007Res"></Qyqyyt>
        </div>
    </div>
</template>

<style lang="scss" scoped>
@use '@/styles/element-lead.scss';

.dashboard {
    min-width: 1440px;
    height: 50vh;
    box-sizing: border-box;
    background-color: #f7f7f7;
}
li {
    list-style-type: none;
    padding-left: 0;
}
.top-left {
    width: 70%;
}
.top-right {
    width: 29%;
}
</style>
