<script lang="ts" setup>
import ModuleTitle from '@/views/home/<USER>/components/ModuleTitle.vue'
import homeService from '@/service/homeService'
import type { Model001ResInfo } from '@/types/home'
import type { RootState } from '@/types/store'
import { useStore } from 'vuex'
const store = useStore<RootState>()

import { ref, onMounted, reactive, computed } from 'vue'
import * as echarts from 'echarts'
const chartRef = ref(null)
interface SelectOption {
    label: string
    key: string
}
const selectOptions = ref<SelectOption[]>([
    {
        label: '上市企业',
        key: 'ssqy',
    },
    {
        label: '融资企业',
        key: 'rzqy',
    },
])

const selectVal = ref<'ssqy' | 'rzqy'>('ssqy')
type IListItem = {
    name: string
    value: number | string
}
const ssqyList = ref<IListItem[]>([])
const rzqyList = ref<IListItem[]>([])
const hasChartData = ref(true)
const option = reactive({
    legend: {
        icon: 'circle', // 图例标记改为圆形
        bottom: '5%',
        left: 'center',
    },
    series: [
        {
            name: '上市融资企业',
            type: 'pie',
            radius: ['50%', '70%'],
            avoidLabelOverlap: false, // 是否避免标签重叠
            emphasis: {
                //高亮状态配置
                label: {
                    show: true,
                    fontSize: 14,
                },
            },
            label: {
                show: true,
                position: 'outside', // 标签显示在扇形外部
                formatter: '{d}%', // {b} 表示数据名称，{d} 表示百分比
            },
            labelLine: {
                //标签引导线配置
                show: true,
                // length: 30, // 视觉引导线第一段的长度
                // length2: 100, // 视觉引导线第二段的长度
                smooth: true, // 是否平滑视觉引导线
                lineStyle: {
                    width: 2,
                },
            },
            data: <IListItem[]>[
                { value: '98.53', name: '主板' },
                { value: '1.47', name: '新三板' },
            ],
        },
    ],
})
const Model002Res = ref<Model001ResInfo[]>([])
const tenantInfo = computed(() => {
    const { account } = store.state.user || {}
    const { tenant } = account || {}
    return tenant
})
onMounted(() => {
    const checkTenant = async () => {
        if (tenantInfo.value) {
            await getModel2Data()
            initChart()
        } else {
            setTimeout(checkTenant, 10)
        }
    }
    checkTenant()
})
const getModel2Data = async () => {
    await homeService
        .searchHomeData({
            code: tenantInfo.value?.areaCode ? tenantInfo.value?.areaCode : '3201',
            roundName: '',
            industryCode: '',
            params: [
                {
                    modelCode: 'Model002',
                    code: [tenantInfo.value?.areaCode ? tenantInfo.value?.areaCode : '3201'],
                    roundName: [],
                    industryCode: [],
                },
            ],
        })
        .then((res) => {
            const { Model002 } = res
            if (Model002?.length) {
                Model002Res.value = Model002.map((item) => {
                    const [title, value] = Object.entries(item)[0]
                    return {
                        ...value,
                        title: title.split('-')[0],
                        indexName: title.split('-')[1],
                    } as Model001ResInfo
                })
                ssqyList.value = Model002Res.value
                    .filter((item) => item.title === '上市企业')
                    .map((item) => ({
                        name: item.indexName,
                        value: item.indexValue,
                    }))
                rzqyList.value = Model002Res.value
                    .filter((item) => item.title === '融资企业')
                    .map((item) => ({
                        name: item.indexName,
                        value: item.indexValue,
                    }))
            }
        })
}
const initChart = () => {
    const chartDom = chartRef.value
    const myChart = echarts.init(chartDom)
    if (
        selectVal.value === 'ssqy' &&
        ssqyList.value.length &&
        ssqyList.value.some((item) => item.value !== null && item.value !== '')
    ) {
        hasChartData.value = true
        option.series[0].data = ssqyList.value
        myChart.setOption(option)
    } else if (
        selectVal.value === 'rzqy' &&
        rzqyList.value.length &&
        rzqyList.value.some((item) => item.value !== null && item.value !== '')
    ) {
        hasChartData.value = true
        option.series[0].data = rzqyList.value
        myChart.setOption(option)
    } else {
        // 暂无相关数据
        hasChartData.value = false
    }
}

const changeSelect = () => {
    initChart()
}
</script>
<template>
    <div class="contain">
        <div class="display-flex space-between">
            <ModuleTitle title="上市融资企业" style="margin-bottom: 30px"></ModuleTitle>
            <el-select v-model="selectVal" placeholder="请选择" size="small" style="width: 140px" @change="changeSelect">
                <el-option v-for="item in selectOptions" :key="item.key" :label="item.label" :value="item.key" />
            </el-select>
        </div>
        <div v-show="hasChartData" ref="chartRef" class="flex-1"></div>
        <div v-show="!hasChartData" class="flex-1 no-data"></div>
    </div>
</template>
<style scoped lang="scss">
.contain {
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    width: 100%;
    height: 100%;
    padding: 16px;
    background-color: #fff;
}
.no-data {
    background: url('@/assets/images/no-chart-data.png');
    background-size: contain;
    background-repeat: no-repeat;
    background-position-x: center;
}
.el-select-dropdown__item.is-selected {
    color: var(--main-blue-);
}
</style>
