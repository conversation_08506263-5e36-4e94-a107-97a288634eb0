<script lang="ts" setup>
import { reactive, ref, onMounted } from 'vue'

import { DATA_CHANNEL_TABLE_COLUMNS } from '@/js/table-options'

import systemService from '@/service/systemService'

import type { IGetCrmLeadParams, ILeadColumn } from '@/types/lead'
import type { TableInstance } from 'element-plus'
import type { ITenantPageParams, ITenantPageItem } from '@/types/tenant'

import SearchBox from '@/components/common/SearchBox.vue'
import EditSourceForm from '@/views/system-management/data-channel/components/EditSourceForm.vue'

const updateSearchParams = (params: IGetCrmLeadParams) => {
    queryParams = params
    search()
}
onMounted(async () => {
    getTableHeight()
    search()
})
const mainContentRef = ref<HTMLDivElement | null>(null)
const searchContentRef = ref<HTMLDivElement | null>(null)
// 获取table高度
const getTableHeight = () => {
    if (mainContentRef.value && searchContentRef.value) {    
        tableHeight.value = mainContentRef.value.clientHeight - searchContentRef.value.clientHeight - 32 - 16 - 16 - 16 - 16 - 16
    }
}
const tableHeight = ref(500)
const tableData = ref<ITenantPageItem[]>([])
const tableAllOptions = ref<ILeadColumn[]>(DATA_CHANNEL_TABLE_COLUMNS)
const tableLoading = ref(false)
const tableListRef = ref<TableInstance>()
let pageInfo = reactive({
    page: 1,
    pageSize: 20,
    total: 0,
})
let queryParams = reactive<ITenantPageParams>({
    page: 1,
    pageSize: 20,
})
const pageChange = (currentPage: number, pageSize: number) => {
    queryParams.page = currentPage
    queryParams.pageSize = pageSize
    search()
}
const search = async () => {
    tableLoading.value = true
    queryParams.page = pageInfo.page
    queryParams.pageSize = pageInfo.pageSize
    console.log('搜索的请求', queryParams)
    let searchRes = await systemService.tenantPage(queryParams)
    console.log('搜索的结果', searchRes)
    if (searchRes.errCode === 0) {
        tableLoading.value = false
        tableData.value = searchRes.data ? searchRes.data : []
        pageInfo.total = searchRes.total
    }
}
/* 操作区域逻辑 */
const formDialog=ref(false)
const tenantFormType = ref('datasource')
const chooseTenant = ref<Partial<ITenantPageItem>>({})
const handleEdit = (row: ITenantPageItem, val: string) => {
    tenantFormType.value = val
    chooseTenant.value = row
    formDialog.value = true
}
const handleCloseFormVisible = () => {
    formDialog.value = false
    search()
}
</script>
<template>
    <div ref="mainContentRef" class="height-100 oa">
        <!-- 搜索栏 -->
        <div ref="searchContentRef" class="b-margin-16 all-padding-16" style="background-color: #fff;">
            <SearchBox
                :searchOptionKey="'DATA_CHANGE_SEARCH_OPTIONS'"
                @updateSearchParams="updateSearchParams"
            ></SearchBox>
        </div>
        <!-- 表格栏 -->
        <div class="all-padding-16" style="background-color: #fff; box-sizing: border-box">
            <!-- 表格 -->
            <el-table
                ref="tableListRef"
                :height="tableHeight+'px'"
                :data="tableData"
                v-loading="tableLoading"
                row-key="id"
                header-row-class-name="tableHeader"
            >
                <el-table-column
                    v-for="columns in tableAllOptions.filter((item) => item.isShow === true)"
                    :key="columns.key"
                    :prop="columns.prop"
                    :label="columns.label"
                    :width="columns.width"
                    :type="columns.type"
                    :fixed="columns.fixed"
                    :sortable="columns.sortable"
                    show-overflow-tooltip
                >
                    <template #default="scope">
                        <el-dropdown  v-if="columns.prop === 'status'" placement="bottom-end" trigger="click">
                            <el-tag effect="plain" :type="scope.row.status === 1 ? 'success' : 'info'">{{ scope.row.status === 1 ? '已启用' : '已停用' }} 
                            </el-tag>
                        </el-dropdown>
                        <div v-if="columns.prop === 'action'" class="display-flex top-bottom-center space-between">
                            <a class="el-dropdown-link" style="text-decoration: none; margin-right: 10px;" @click="handleEdit(scope.row,'dataSource')">数据源配置</a>
                            <a class="el-dropdown-link" style="text-decoration: none" @click="handleEdit(scope.row,'collectWay')">采集通道配置</a>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
            <!-- 分页器 -->
            <el-affix position="bottom" :offset="0">
                <div class="pagination-bar">
                    <el-pagination
                        v-model:currentPage="pageInfo.page"
                        v-model:page-size="pageInfo.pageSize"
                        :total="pageInfo.total"
                        :page-sizes="[20, 40, 60, 100]"
                        layout="total, sizes, prev, pager, next, jumper"
                        @change="pageChange"
                    />
                </div>
            </el-affix>
        </div>
        <EditSourceForm :visible="formDialog" :type="tenantFormType" :chooseTenant="chooseTenant" @closeVisible="handleCloseFormVisible()"></EditSourceForm>
    </div>
</template>
<style scoped lang="scss">
@use '@/styles/element-lead.scss';
.action-bar {
    color: #303133;
    margin-bottom: 16px;
    flex-direction: row-reverse;
    .choose-content {
        font-size: 14px;
        color: #a6a6a6;
        margin-right: 16px;
    }
    .turn-content {
        margin-right: 16px;
    }
}

.el-dropdown-link {
    display: block;
    cursor: pointer;
    color: var(--main-blue-);
    font-size: 16px;
}
:deep(.el-dropdown-menu__item) {
    padding: 2px 16px;
}
:deep(.el-table.is-scrolling-left th.el-table-fixed-column--left) {
    background-color: #f5f7fa;
}
:deep(.el-table__header-wrapper tr th.el-table-fixed-column--left) {
    background-color: #f5f7fa;
}

.tableHeader {
    font-size: 24px;
}

.pagination-bar {
    display: flex;
    flex-direction: row-reverse;
    width: 100%;
    background-color: #fff;
    padding-top: 16px;
    padding-bottom: 16px;
    --el-text-color-regular: #303133;
}
</style>
