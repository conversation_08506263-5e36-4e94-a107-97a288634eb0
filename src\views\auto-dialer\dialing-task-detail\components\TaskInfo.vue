<script lang="ts" setup>
import commonData from '@/js/common-data'
import type { IAutoDialerTaskDetailInfoItem } from '@/types/autoDialer'
import { convertToEnumLabels } from '@/utils/format'
import { safeGetValueByKey } from '@/utils/obj-safe-get'
import { ref } from 'vue'
// ====================== Interfaces & Types ======================

// ====================== Props & Injections ======================
const props = defineProps<{
    taskDetail: IAutoDialerTaskDetailInfoItem | null
}>()

// ====================== Store & Computed Values ======================

// ====================== Refs & Reactive State ======================
const activeName = ref('info')
const keys = ref([
    { key: 'taskName', label: '外呼任务名称', width: 8 },
    { key: 'outboundDate', label: '外呼开始时间', width: 8 },
    { key: 'outboundExpireDate', label: '外呼结束时间', width: 8 },
    { key: 'robotName', label: '机器人话术', width: 8 },
    { key: 'outboundTimeInterval', label: '外呼时间段', width: 16 },
    { key: 'allowRecallStatus', label: '自动重呼', width: 24 },
])

// ====================== Methods ======================
const getDataValue = (key: string) => {
    if (!props.taskDetail) return ''
    let value = safeGetValueByKey(props.taskDetail, key)?.toString() || '-'
    if (key === 'allowRecallStatus') {
        const recallStatus = safeGetValueByKey(props.taskDetail, 'recallStatus')
        if (recallStatus === 0) return '关闭'
        const recallPeriodMin = safeGetValueByKey(props.taskDetail, 'recallPeriodMin')?.toString() || ''
        value = convertToEnumLabels(value, commonData.recallStatusDicts)
        if (!value || value.length === 0) value = ''
        if (value !== '' && recallPeriodMin !== '') value = value + `,间隔时间${recallPeriodMin}分钟`
        if (value === '' && recallPeriodMin !== '') value = `间隔时间${recallPeriodMin}分钟`
        if (value === '' && recallPeriodMin === '') value = '-'
    }
    return value
}
// ====================== Watchers ======================

// ====================== Lifecycle Hooks ======================
</script>

<template>
    <div class="back-color-white all-padding-16 border-radius-4">
        <el-tabs v-model="activeName">
            <el-tab-pane label="任务详情" name="info">
                <el-row class="row-gap-16 t-padding-24">
                    <el-col
                        :xs="24"
                        :sm="12"
                        :lg="item.width"
                        class="flex flex-row gap-16 r-padding-8"
                        v-for="(item, index) in keys"
                        :key="index"
                    >
                        <div class="font-16 color-text-grey mw-110">{{ item.label }}</div>
                        <div class="font-16 color-text-black flex-1 text-ellipsis">
                            {{ getDataValue(item.key) }}
                        </div>
                    </el-col>
                </el-row>
            </el-tab-pane>
        </el-tabs>
    </div>
</template>

<style lang="scss" scoped></style>
