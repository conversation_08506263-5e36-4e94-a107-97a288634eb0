// src/composables/useFixedActionBar.ts
import { ref, onMounted, onBeforeUnmount } from 'vue'

export function useFixedActionBar(
    tableContentRef: Ref<HTMLElement | null>,
) {
    const distanceFromTop = ref(0)

    const handleScroll = () => {
        if (!tableContentRef.value) return

        const rect = tableContentRef.value.getBoundingClientRect()
        distanceFromTop.value = rect.top

        const actionBar = document.querySelector('.action-bar')
        const scrollContainer = document.querySelector('.oa')
        if (!actionBar || !scrollContainer) return

        if (distanceFromTop.value < 100) {
            actionBar.classList.add('action-bar-fixed')
            const containerWidth = scrollContainer.getBoundingClientRect().width
            actionBar.style.width = `${containerWidth + 32}px`
        } else {
            actionBar.classList.remove('action-bar-fixed')
            actionBar.style.width = 'auto'
        }
    }

    onMounted(() => {
        const container = document.querySelector('.oa')
        container?.addEventListener('scroll', handleScroll)
    })

    onBeforeUnmount(() => {
        const container = document.querySelector('.oa')
        container?.removeEventListener('scroll', handleScroll)
    })
}
