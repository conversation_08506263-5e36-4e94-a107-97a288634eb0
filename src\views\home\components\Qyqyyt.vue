<script lang="ts" setup>
import ModuleTitle from '@/views/home/<USER>/components/ModuleTitle.vue'
import type { ModelRes } from '@/types/home'
import { watch } from 'vue'
const props = defineProps<{
    data: ModelRes[]
}>()
const dataList = [
    {
        groupName: '区域企业总览',
        children: [
            {
                label: '存续企业总数',
                value: '',
            },
            {
                label: '规上企业',
                value: '',
            },
        ],
    },
    {
        groupName: '投资创收概览',
        children: [
            {
                label: '本年度新增投资总额',
                value: '',
            },
            {
                label: '资产总额',
                value: '',
            },
            {
                label: '资产总额中位数',
                value: '',
            },
            {
                label: '实现营业收入',
                value: '',
            },
            {
                label: '实现利润总额',
                value: '',
            },
            {
                label: '利润总额中位数',
                value: '',
            },
        ],
    },
    {
        groupName: '社保就业情况概览',
        children: [
            {
                label: '实行就业人数',
                value: '',
            },
            {
                label: '新增就业人数',
                value: '',
            },
            {
                label: '总社保人数',
                value: '',
            },
            {
                label: '社保人数中位数',
                value: '',
            },
            {
                label: '平均工资',
                value: '',
            },
            {
                label: '中位数工资',
                value: '',
            },
        ],
    },
    {
        groupName: '税收情况概览',
        children: [
            {
                label: '税收总额',
                value: '',
            },
            {
                label: '税收中位数',
                value: '',
            },
            {
                label: '税收同比',
                value: '',
            },
        ],
    },
]
watch(
    () => props.data,
    (newVal) => {
        if (newVal) {
            props.data.map((item) => {
                const [key, value] = Object.entries(item)[0]
                dataList.forEach((item) => {
                    let childrenItem = item.children.filter((o) => o.label === key)
                    if (childrenItem.length) {
                        childrenItem[0].value = value.indexValue
                    }
                })
            })
        }
    },
    { immediate: true }
)
</script>
<template>
    <div class="qyqyyt">
        <div class="b-margin-24">
            <ModuleTitle title="全域企业云图"></ModuleTitle>
        </div>
        <ul class="display-flex space-between">
            <li class="qyqyyt-contant" v-for="(item, index) in dataList" :key="index">
                <div class="content-header">{{ item.groupName }}</div>
                <div class="display-flex left-right-center flex-wrap">
                    <div class="content-item" v-for="(obj, index) in item.children" :key="index">
                        <div class="text-center item-title">{{ obj.label }}</div>
                        <div class="text-center item-content">{{ obj.value ? Number(obj.value) : '-' }}</div>
                    </div>
                </div>
            </li>
        </ul>
    </div>
</template>
<style scoped lang="scss">
ul {
    padding: 0;
    margin: 0;
    li {
        list-style-type: none;
        padding-left: 0;
    }
}
.qyqyyt {
    padding: 16px;
    background-color: #fff;
    .qyqyyt-contant {
        width: 23%;
        padding: 16px;
        font-size: 14px;
        font-weight: 500;
        background:
            linear-gradient(0deg, rgba(248, 249, 253, 1), rgba(248, 249, 253, 1)),
            linear-gradient(90deg, rgb(238, 241, 249) 0%, rgb(229, 232, 247) 100%);
        .content-header {
            text-align: center;
            color: var(--main-black);
            margin-bottom: 24px;
        }
        .content-item {
            min-width: 110px;
            min-height: 80px;
            .item-title {
                // height: 41px;
                color: var(--two-grey);
                margin-bottom: 12px;
            }
            .item-content {
                font-size: 18px;
                font-weight: 700;
            }
        }
    }
}
</style>
