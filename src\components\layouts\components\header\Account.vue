<script lang="tsx" setup>
import ProfilePanel from './components/profile/ProfilePanel.vue'
import userService from '@/service/userService'
import type { RootState } from '@/types/store'
import { computed, onBeforeMount } from 'vue'
import { useStore } from 'vuex'
import { useRoute } from 'vue-router'
import systemService from '@/service/systemService'

const route = useRoute()
const store = useStore<RootState>()

onBeforeMount(() => {
    console.log('route.query',route.query)
    if(route.query.oemkey){
        systemService.systemOEMDetail({key:route.query.oemkey as string,productType:0}).then((res) => {
            console.log('oem detail',res)
        })
    }
    userService.userGetAccountInfo().then((account) => {
        store.dispatch('user/setAccountInfo', { ...account })
    })
})

const nikename = computed(() => {
    const { account } = store.state.user || {}
    const { user } = account || {}
    const { nickname } = user || {}

    return nickname || ''
})
</script>

<template>
    <el-popover placement="bottom" :show-arrow="false" trigger="click" :width="308" popper-style="padding: 0">
        <template #reference>
            <div class="flex top-bottom-center gap-8 pointer">
                <div class="border-radius-26 color-two-grey back-color-avatar w-26 h-26 flex-center">
                    <el-icon class="color-white width-100"><UserFilled /></el-icon>
                </div>
                <div>{{ nikename }}</div>
                <div class="flex-center">
                    <el-icon class="color-three-grey"><CaretBottom /></el-icon>
                </div>
            </div>
        </template>
        <template #default>
            <ProfilePanel />
        </template>
    </el-popover>
</template>

<style lang="scss" scoped></style>
