<script lang="ts" setup>
import type { IAutoDialerTaskDetailFilter } from '@/types/autoDialer'
import { DialingContactsFilter, DialingContactsTable } from './components'
import { computed, ref } from 'vue'
import { useStore } from 'vuex'
import type { RootState } from '@/types/store'
import OpenPage from '@/components/auto-dialer/open-page/OpenPage.vue'

// ====================== Interfaces & Types ======================

// ====================== Props & Injections ======================

// ====================== Store & Computed Values ======================
const store = useStore<RootState>()

const isOpen = computed(() => {
    const { account } = store.state.user || {}
    const { tenant } = account || {}
    const { openAiPhone } = tenant || {}
    return openAiPhone
})

// ====================== Refs & Reactive State ======================
const filterParams = ref<IAutoDialerTaskDetailFilter>()

// ====================== Methods ======================
const filterQuery = (params: IAutoDialerTaskDetailFilter) => {
    filterParams.value = params
}

// ====================== Watchers ======================

// ====================== Lifecycle Hooks ======================
</script>

<template>
    <div class="flex flex-column gap-16">
        <div class="back-color-white border-radius-4 all-padding-16" v-if="isOpen">
            <DialingContactsFilter :get-data="filterQuery" />
        </div>
        <div class="back-color-white height-100 oh all-padding-16" v-if="isOpen">
            <DialingContactsTable :filter-params="filterParams" />
        </div>
        <OpenPage v-if="!isOpen" />
    </div>
</template>

<style lang="scss" scoped></style>
