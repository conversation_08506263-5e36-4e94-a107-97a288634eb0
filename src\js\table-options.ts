import type { ILeadColumn } from '@/types/lead'

export const LEAD_POOL_TABLE_COLUMNS: ILeadColumn[] = [
    // {
    //     id: 1,
    //     label: '企业名称',
    //     key: 'companyName',
    //     prop: 'companyName',
    //     width: '220',
    //     editable: false,
    //     fixed: 'left',
    //     type: 'default',
    //     isShow: true,
    // },
    // {
    //     id: 2,
    //     label: '线索名称',
    //     key: 'name',
    //     prop: 'name',
    //     width: '220',
    //     editable: true,
    //     fixed: 'left',
    //     type: 'default',
    //     isShow: true,
    // },
    {
        id: 3,
        label: '企业税号',
        key: 'socialCreditCode',
        prop: 'socialCreditCode',
        width: '220',
        editable: true,
        type: 'default',
        isShow: true,
    },
    {
        id: 4,
        label: '风险等级',
        key: 'riskLevel',
        prop: 'riskLevel',
        width: '130',
        sortable: true,
        editable: true,
        type: 'default',
        isShow: true,
    },
    {
        id: 5,
        label: '基础得分',
        key: 'basicScore',
        prop: 'basicScore',
        width: '120',
        editable: true,
        type: 'default',
        isShow: true,
    },
    {
        id: 6,
        label: '负责人姓名',
        key: 'user',
        prop: 'user',
        width: '120',
        editable: true,
        type: 'default',
        isShow: true,
    },
    {
        id: 7,
        label: '手机',
        key: 'contactInfo',
        prop: 'contactInfo.mobile',
        width: '150',
        editable: true,
        type: 'default',
        isShow: true,
    },
    {
        id: 8,
        label: '外呼结果',
        key: 'aiphoneResult',
        prop: 'aiphoneResult',
        width: '120',
        editable: true,
        type: 'default',
        isShow: true,
    },
    {
        id: 9,
        label: '创建时间',
        key: 'createTime',
        prop: 'createTime',
        width: '180',
        sortable: true,
        editable: true,
        type: 'default',
        isShow: true,
    },
    {
        id: 10,
        label: '下次跟进时间',
        key: 'nextFollowDate',
        prop: 'nextFollowDate',
        width: '180',
        sortable: true,
        editable: true,
        type: 'default',
        isShow: true,
    },
    {
        id: 11,
        label: '最新跟进记录',
        key: 'newFollowDescription',
        prop: 'newFollowDescription',
        width: '180',
        editable: true,
        type: 'default',
        isShow: true,
    },
    {
        id: 12,
        label: '跟进状态',
        key: 'status',
        prop: 'status',
        width: '120',
        editable: true,
        type: 'default',
        isShow: true,
    },
    {
        id: 13,
        label: '数据来源',
        key: 'source',
        prop: 'source',
        width: '120',
        editable: true,
        type: 'default',
        isShow: true,
    },
    {
        id: 14,
        label: '标签',
        key: 'tagInfos',
        prop: 'tagInfos',
        width: '250',
        editable: true,
        type: 'default',
        isShow: true,
    },
    {
        id: 15,
        label: '备注',
        key: 'note',
        prop: 'note',
        width: '120',
        editable: true,
        type: 'default',
        isShow: true,
    },
    {
        id: 16,
        label: '发票采集时间',
        key: 'invoiceCollectDate',
        prop: 'invoiceCollectDate',
        width: '160',
        editable: true,
        type: 'default',
        isShow: true,
    },
    {
        id: 17,
        label: '税务采集时间',
        key: 'taxCollectDate',
        prop: 'taxCollectDate',
        width: '160',
        editable: true,
        type: 'default',
        isShow: true,
    },
    {
        id: 18,
        label: '来源企业',
        key: 'sourceCompany',
        prop: 'sourceCompany',
        width: '180',
        editable: true,
        type: 'default',
        isShow: true,
    },
    {
        id: 19,
        label: '实际跟进时间',
        key: 'newFollowDate',
        prop: 'newFollowDate',
        width: '180',
        sortable: true,
        editable: true,
        type: 'default',
        isShow: true,
    },
    {
        id: 20,
        label: '创建人',
        key: 'createUser',
        prop: 'createUser',
        width: '120',
        editable: true,
        type: 'default',
        isShow: true,
    },
    {
        id: 21,
        label: '前负责人',
        key: 'beforeUser',
        prop: 'beforeUser',
        width: '120',
        editable: true,
        type: 'default',
        isShow: true,
    },
    {
        id: 22,
        label: '线索渠道',
        key: 'channel',
        prop: 'channel',
        width: '120',
        editable: true,
        type: 'default',
        isShow: true,
    },
    // {
    //     id: 23,
    //     label: '操作',
    //     key: 'action',
    //     prop: 'action',
    //     width: '120',
    //     fixed: 'right',
    //     editable: false,
    //     type: 'default',
    //     isShow: true,
    // },
]
export const LEAD_LIST_TABLE_COLUMNS: ILeadColumn[] = [
    // {
    //     id: 1,
    //     label: '企业名称',
    //     key: 'companyName',
    //     prop: 'companyName',
    //     width: '220',
    //     editable: false,
    //     fixed: 'left',
    //     type: 'default',
    //     isShow: true,
    // },
    // {
    //     id: 2,
    //     label: '线索名称',
    //     key: 'name',
    //     prop: 'name',
    //     width: '220',
    //     editable: true,
    //     fixed: 'left',
    //     type: 'default',
    //     isShow: true,
    // },
    {
        id: 3,
        label: '企业税号',
        key: 'socialCreditCode',
        prop: 'socialCreditCode',
        width: '220',
        editable: true,
        type: 'default',
        isShow: true,
    },
    {
        id: 4,
        label: '风险等级',
        key: 'riskLevel',
        prop: 'riskLevel',
        width: '130',
        sortable: true,
        editable: true,
        type: 'default',
        isShow: true,
    },
    {
        id: 5,
        label: '基础得分',
        key: 'basicScore',
        prop: 'basicScore',
        width: '120',
        editable: true,
        type: 'default',
        isShow: true,
    },
    {
        id: 6,
        label: '负责人姓名',
        key: 'user',
        prop: 'user',
        width: '120',
        editable: true,
        type: 'default',
        isShow: true,
    },
    {
        id: 7,
        label: '手机',
        key: 'contactInfo',
        prop: 'contactInfo.mobile',
        width: '150',
        editable: true,
        type: 'default',
        isShow: true,
    },
    {
        id: 8,
        label: '外呼结果',
        key: 'aiphoneResult',
        prop: 'aiphoneResult',
        width: '120',
        editable: true,
        type: 'default',
        isShow: true,
    },
    {
        id: 9,
        label: '创建时间',
        key: 'createTime',
        prop: 'createTime',
        width: '180',
        sortable: true,
        editable: true,
        type: 'default',
        isShow: true,
    },
    {
        id: 10,
        label: '下次跟进时间',
        key: 'nextFollowDate',
        prop: 'nextFollowDate',
        width: '180',
        sortable: true,
        editable: true,
        type: 'default',
        isShow: true,
    },
    {
        id: 11,
        label: '最新跟进记录',
        key: 'newFollowDescription',
        prop: 'newFollowDescription',
        width: '180',
        editable: true,
        type: 'default',
        isShow: true,
    },
    {
        id: 12,
        label: '跟进状态',
        key: 'status',
        prop: 'status',
        width: '120',
        editable: true,
        type: 'default',
        isShow: true,
    },
    {
        id: 13,
        label: '数据来源',
        key: 'source',
        prop: 'source',
        width: '120',
        editable: true,
        type: 'default',
        isShow: true,
    },
    {
        id: 14,
        label: '标签',
        key: 'tagInfos',
        prop: 'tagInfos',
        width: '250',
        editable: true,
        type: 'default',
        isShow: true,
    },
    {
        id: 15,
        label: '备注',
        key: 'note',
        prop: 'note',
        width: '120',
        editable: true,
        type: 'default',
        isShow: true,
    },
    {
        id: 16,
        label: '发票采集时间',
        key: 'invoiceCollectDate',
        prop: 'invoiceCollectDate',
        width: '160',
        editable: true,
        type: 'default',
        isShow: true,
    },
    {
        id: 17,
        label: '税务采集时间',
        key: 'taxCollectDate',
        prop: 'taxCollectDate',
        width: '160',
        editable: true,
        type: 'default',
        isShow: true,
    },
    {
        id: 18,
        label: '来源企业',
        key: 'sourceCompany',
        prop: 'sourceCompany',
        width: '180',
        editable: true,
        type: 'default',
        isShow: true,
    },
    {
        id: 19,
        label: '实际跟进时间',
        key: 'newFollowDate',
        prop: 'newFollowDate',
        width: '180',
        sortable: true,
        editable: true,
        type: 'default',
        isShow: true,
    },
    {
        id: 20,
        label: '创建人',
        key: 'createUser',
        prop: 'createUser',
        width: '120',
        editable: true,
        type: 'default',
        isShow: true,
    },
    {
        id: 21,
        label: '前负责人',
        key: 'beforeUser',
        prop: 'beforeUser',
        width: '120',
        editable: true,
        type: 'default',
        isShow: true,
    },
    {
        id: 22,
        label: '线索渠道',
        key: 'channel',
        prop: 'channel',
        width: '120',
        editable: true,
        type: 'default',
        isShow: true,
    },
    // {
    //     id: 23,
    //     label: '操作',
    //     key: 'action',
    //     prop: 'action',
    //     width: '150',
    //     fixed: 'right',
    //     editable: false,
    //     type: 'default',
    //     isShow: true,
    // },
]
export const CUSTOMER_POOL_TABLE_COLUMNS: ILeadColumn[] = [
    // {
    //     id: 1,
    //     type: 'default',
    //     label: '企业名称',
    //     key: 'companyName',
    //     prop: 'companyName',
    //     width: '180',
    //     editable: true,
    //     fixed: 'left',
    //     isShow: true,
    // },
    {
        id: 3,
        type: 'default',
        label: '企业税号',
        key: 'socialCreditCode',
        prop: 'socialCreditCode',
        width: '180',
        editable: true,
        isShow: true,
    },
    {
        id: 4,
        type: 'default',
        label: '风险等级',
        key: 'riskLevel',
        prop: 'riskLevel',
        width: '130',
        sortable: true,
        editable: true,
        isShow: true,
    },
    {
        id: 5,
        type: 'default',
        label: '基础得分',
        key: 'basicScore',
        prop: 'basicScore',
        width: '120',
        editable: true,
        isShow: true,
    },
    {
        id: 6,
        type: 'default',
        label: '负责人姓名',
        key: 'user',
        prop: 'user',
        width: '120',
        editable: true,
        isShow: true,
    },
    {
        id: 7,
        type: 'default',
        label: '手机',
        key: 'contactInfo',
        prop: 'contactInfo.mobile',
        width: '150',
        editable: true,
        isShow: true,
    },
    {
        id: 8,
        type: 'default',
        label: '外呼结果',
        key: 'aiphoneResult',
        prop: 'aiphoneResult',
        width: '120',
        editable: true,
        isShow: true,
    },
    {
        id: 9,
        type: 'default',
        label: '创建时间',
        key: 'createTime',
        prop: 'createTime',
        width: '180',
        sortable: true,
        editable: true,
        isShow: true,
    },
    {
        id: 10,
        type: 'default',
        label: '下次跟进时间',
        key: 'nextFollowDate',
        prop: 'nextFollowDate',
        width: '180',
        sortable: true,
        editable: true,
        isShow: true,
    },
    {
        id: 11,
        type: 'default',
        label: '最新跟进记录',
        key: 'newFollowDescription',
        prop: 'newFollowDescription',
        width: '180',
        editable: true,
        isShow: true,
    },
    {
        id: 12,
        type: 'default',
        label: '跟进状态',
        key: 'status',
        prop: 'status',
        width: '120',
        editable: true,
        isShow: true,
    },
    {
        id: 13,
        type: 'default',
        label: '数据来源',
        key: 'source',
        prop: 'source',
        width: '120',
        editable: true,
        isShow: true,
    },
    {
        id: 14,
        type:'default',
        label: '标签',
        key: 'tagInfos',
        prop: 'tagInfos',
        width:'250',
        editable: true,
        isShow: true,
    },
    {
        id: 15,
        type: 'default',
        label: '备注',
        key: 'note',
        prop: 'note',
        width: '120',
        editable: true,
        isShow: true,
    },
    {
        id: 16,
        type: 'default',
        label: '发票采集时间',
        key: 'invoiceCollectDate',
        prop: 'invoiceCollectDate',
        width: '160',
        editable: true,
        isShow: true,
    },
    {
        id: 17,
        type: 'default',
        label: '税务采集时间',
        key: 'taxCollectDate',
        prop: 'taxCollectDate',
        width: '160',
        editable: true,
        isShow: true,
    },
    {
        id: 19,
        type: 'default',
        label: '来源企业',
        key: 'sourceCompany',
        prop: 'sourceCompany',
        width: '180',
        editable: true,
        isShow: true,
    },
    {
        id: 21,
        type: 'default',
        label: '实际跟进时间',
        key: 'newFollowDate',
        prop: 'newFollowDate',
        width: '180',
        sortable: true,
        editable: true,
        isShow: true,
    },
    {
        id: 22,
        type: 'default',
        label: '创建人',
        key: 'createUser',
        prop: 'createUser',
        width: '120',
        editable: true,
        isShow: true,
    },
    {
        id: 23,
        type: 'default',
        label: '前负责人',
        key: 'beforeUser',
        prop: 'beforeUser',
        width: '120',
        editable: true,
        isShow: true,
    },
    {
        id: 24,
        type: 'default',
        label: '客户渠道',
        key: 'channel',
        prop: 'channel',
        width: '120',
        editable: true,
        isShow: true,
    },
    {
        id: 25,
        type: 'default',
        label: '最新转入时间',
        key: 'turnCustomerPoolDate',
        prop: 'turnCustomerPoolDate',
        width: '180',
        editable: true,
        isShow: true,
    },
    // {
    //     id: 18,
    //     type: 'default',
    //     label: '操作',
    //     key: 'action',
    //     prop: 'action',
    //     width: '150',
    //     fixed: 'right',
    //     editable: false,
    //     isShow: true,
    // },
]
export const CUSTOMER_LIST_TABLE_COLUMNS: ILeadColumn[] = [
    // {
    //     id: 1,
    //     type: 'default',
    //     label: '企业名称',
    //     key: 'companyName',
    //     prop: 'companyName',
    //     width: '180',
    //     editable: true,
    //     fixed: 'left',
    //     isShow: true,
    // },
    {
        id: 3,
        type: 'default',
        label: '企业税号',
        key: 'socialCreditCode',
        prop: 'socialCreditCode',
        width: '180',
        editable: true,
        isShow: true,
    },
    {
        id: 4,
        type: 'default',
        label: '风险等级',
        key: 'riskLevel',
        prop: 'riskLevel',
        width: '130',
        sortable: true,
        editable: true,
        isShow: true,
    },
    {
        id: 5,
        type: 'default',
        label: '基础得分',
        key: 'basicScore',
        prop: 'basicScore',
        width: '120',
        editable: true,
        isShow: true,
    },
    {
        id: 6,
        type: 'default',
        label: '负责人姓名',
        key: 'user',
        prop: 'user',
        width: '120',
        editable: true,
        isShow: true,
    },
    {
        id: 7,
        type: 'default',
        label: '手机',
        key: 'contactInfo',
        prop: 'contactInfo.mobile',
        width: '150',
        editable: true,
        isShow: true,
    },
    {
        id: 8,
        type: 'default',
        label: '外呼结果',
        key: 'aiphoneResult',
        prop: 'aiphoneResult',
        width: '120',
        editable: true,
        isShow: true,
    },
    {
        id: 9,
        type: 'default',
        label: '创建时间',
        key: 'createTime',
        prop: 'createTime',
        width: '180',
        sortable: true,
        editable: true,
        isShow: true,
    },
    {
        id: 10,
        type: 'default',
        label: '下次跟进时间',
        key: 'nextFollowDate',
        prop: 'nextFollowDate',
        width: '180',
        sortable: true,
        editable: true,
        isShow: true,
    },
    {
        id: 11,
        type: 'default',
        label: '最新跟进记录',
        key: 'newFollowDescription',
        prop: 'newFollowDescription',
        width: '180',
        editable: true,
        isShow: true,
    },
    {
        id: 12,
        type: 'default',
        label: '跟进状态',
        key: 'status',
        prop: 'status',
        width: '120',
        editable: true,
        isShow: true,
    },
    {
        id: 13,
        type: 'default',
        label: '数据来源',
        key: 'source',
        prop: 'source',
        width: '120',
        editable: true,
        isShow: true,
    },
    {
        id: 14,
        type:'default',
        label: '标签',
        key: 'tagInfos',
        prop: 'tagInfos',
        width:'250',
        editable: true,
        isShow: true,
    },
    {
        id: 15,
        type: 'default',
        label: '备注',
        key: 'note',
        prop: 'note',
        width: '120',
        editable: true,
        isShow: true,
    },
    {
        id: 16,
        type: 'default',
        label: '发票采集时间',
        key: 'invoiceCollectDate',
        prop: 'invoiceCollectDate',
        width: '160',
        editable: true,
        isShow: true,
    },
    {
        id: 17,
        type: 'default',
        label: '税务采集时间',
        key: 'taxCollectDate',
        prop: 'taxCollectDate',
        width: '160',
        editable: true,
        isShow: true,
    },
    {
        id: 19,
        type: 'default',
        label: '来源企业',
        key: 'sourceCompany',
        prop: 'sourceCompany',
        width: '180',
        editable: true,
        isShow: true,
    },
    {
        id: 20,
        type: 'default',
        label: '协作人',
        key: 'ministrantInfos',
        prop: 'ministrantInfos',
        width: '180',
        editable: true,
        isShow: true,
    },
    {
        id: 21,
        type: 'default',
        label: '实际跟进时间',
        key: 'newFollowDate',
        prop: 'newFollowDate',
        width: '180',
        sortable: true,
        editable: true,
        isShow: true,
    },
    {
        id: 22,
        type: 'default',
        label: '创建人',
        key: 'createUser',
        prop: 'createUser',
        width: '120',
        editable: true,
        isShow: true,
    },
    {
        id: 23,
        type: 'default',
        label: '前负责人',
        key: 'beforeUser',
        prop: 'beforeUser',
        width: '120',
        editable: true,
        isShow: true,
    },
    {
        id: 24,
        type: 'default',
        label: '客户渠道',
        key: 'channel',
        prop: 'channel',
        width: '120',
        editable: true,
        isShow: true,
    },
    {
        id: 25,
        type: 'default',
        label: '最新转入时间',
        key: 'turnCustomerDate',
        prop: 'turnCustomerDate',
        width: '180',
        editable: true,
        isShow: true,
    },
    // {
    //     id: 18,
    //     type: 'default',
    //     label: '操作',
    //     key: 'action',
    //     prop: 'action',
    //     width: '150',
    //     fixed: 'right',
    //     editable: false,
    //     isShow: true,
    // },
]
export const TENANT_LIST_TABLE_COLUMNS: ILeadColumn[] = [
    {
        id: 1,
        label: '租户名称',
        key: 'name',
        prop: 'name',
        editable: false,
        fixed: 'left',
        type: 'default',
        isShow: true,
    },
    {
        id: 2,
        label: '联系人',
        key: 'contact',
        prop: 'contact',
        editable: true,
        fixed: 'left',
        type: 'default',
        isShow: true,
    },
    {
        id: 3,
        label: '状态',
        key: 'status',
        prop: 'status',
        editable: true,
        type: 'default',
        isShow: true,
    },
    {
        id: 4,
        label: '联系电话',
        key: 'phone',
        prop: 'phone',
        editable: true,
        type: 'default',
        isShow: true,
    },
    {
        id: 5,
        label: '地区编码',
        key: 'areaCode',
        prop: 'areaCode',
        editable: true,
        type: 'default',
        isShow: true,
    },
    {
        id: 6,
        label: '地区名称',
        key: 'areaName',
        prop: 'areaName',
        editable: true,
        type: 'default',
        isShow: true,
    },
    {
        id: 7,
        label: '登录账号',
        key: 'username',
        prop: 'username',
        editable: true,
        type: 'default',
        isShow: true,
    },
    {
        id: 8,
        label: '创建人',
        key: 'createUserName',
        prop: 'createUserName',
        editable: true,
        type: 'default',
        isShow: true,
    },
    {
        id: 23,
        label: '操作',
        width: '140',
        key: 'action',
        prop: 'action',
        fixed: 'right',
        editable: false,
        type: 'default',
        isShow: true,
    },
]
export const NOTIFICATION_LIST_TABLE_COLUMNS: ILeadColumn[] = [
    {
        id: 1,
        label: '消息通知',
        key: 'content',
        prop: 'content',
        editable: false,
        fixed: 'left',
        type: 'default',
        isShow: true,
    },
    {
        id: 2,
        label: '类型',
        key: 'msgType',
        prop: 'msgType',
        editable: true,
        type: 'default',
        isShow: true,
    },
    {
        id: 3,
        label: '时间',
        width: '240',
        key: 'createTime',
        prop: 'createTime',
        editable: true,
        type: 'default',
        isShow: true, 
    },
    {
        id: 24,
        label: '操作',
        width: '120',
        key: 'action',
        prop: 'action',
        fixed: 'right',
        editable: false,
        type: 'default',
        isShow: true,
    },
]
export const EQUITIES_LIST_TABLE_COLUMNS: ILeadColumn[] = [
    {
        id: 1,
        label: '线索权益',
        key: 'service_name',
        prop: 'service_name',
        editable: false,
        type: 'default',
        isShow: true,
    },
    {
        id: 2,
        label: '权益数量',
        key: 'quantity',
        prop: 'quantity',
        editable: false,
        type: 'default',
        isShow: true,
    },
    {
        id: 1,
        label: '剩/总',
        key: 'balance',
        prop: 'balance',
        editable: false,
        type: 'default',
        isShow: true,
    },
    {
        id: 1,
        label: '入账时间',
        key: 'created_at',
        prop: 'created_at',
        editable: false,
        type: 'default',
        isShow: true,
    },
    {
        id: 1,
        label: '到期时间',
        key: 'expire_time',
        prop: 'expire_time',
        editable: false,
        type: 'default',
        isShow: true,
    },
]
export const DATA_CHANNEL_TABLE_COLUMNS: ILeadColumn[] = [
    {
        id: 1,
        label: '租户名称',
        key: 'name',
        prop: 'name',
        editable: false,
        fixed: 'left',
        type: 'default',
        isShow: true,
    },
    {
        id: 2,
        label: '联系人',
        key: 'contact',
        prop: 'contact',
        editable: true,
        fixed: 'left',
        type: 'default',
        isShow: true,
    },
    {
        id: 3,
        label: '状态',
        key: 'status',
        prop: 'status',
        editable: true,
        type: 'default',
        isShow: true,
    },
    {
        id: 4,
        label: '联系电话',
        key: 'phone',
        prop: 'phone',
        editable: true,
        type: 'default',
        isShow: true,
    },
    {
        id: 5,
        label: '地区编码',
        key: 'areaCode',
        prop: 'areaCode',
        editable: true,
        type: 'default',
        isShow: true,
    },
    {
        id: 6,
        label: '地区名称',
        key: 'areaName',
        prop: 'areaName',
        editable: true,
        type: 'default',
        isShow: true,
    },
    {
        id: 7,
        label: '登录账号',
        key: 'username',
        prop: 'username',
        editable: true,
        type: 'default',
        isShow: true,
    },
    {
        id: 8,
        label: '创建人',
        key: 'createUserName',
        prop: 'createUserName',
        editable: true,
        type: 'default',
        isShow: true,
    },
    {
        id: 23,
        label: '操作',
        width: '220',
        key: 'action',
        prop: 'action',
        fixed: 'right',
        editable: false,
        type: 'default',
        isShow: true,
    },
]
