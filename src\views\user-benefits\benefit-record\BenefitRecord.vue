<template>
    <div style="background-color: #f7f7f7; ">
        <div style=" background-color: #fff; padding: 16px">
            <!-- 搜索公共组件 -->
            <searchBox
                :searchOptionKey="searchOptionsKey"
                @updateSearchParams="updateSearchParams"
                :defaultValue="defaultQueryParams"
                :customConfig="searchConfig"
            >
            </searchBox>
        </div>
        <div class="t-margin-16" style="background-color: #fff; padding: 16px; box-sizing: border-box">
            <div class="flex flex-row b-margin-16">
                <el-dropdown placement="bottom-start">
                    <el-button :loading="exporting" :disabled="exporting">
                        导出
                        <el-icon class="l-margin-8"><CaretBottom /></el-icon>
                    </el-button>
                    <template #dropdown>
                        <el-dropdown-menu>
                            <!-- <el-dropdown-item @click="exportRecord()">导出所选</el-dropdown-item>
                            <el-dropdown-item @click="exportRecord(100)">导出前100条</el-dropdown-item>
                            <el-dropdown-item @click="exportRecord(500)">导出前500条</el-dropdown-item> -->
                        </el-dropdown-menu>
                    </template>
                </el-dropdown>
            </div>
            <el-table
                ref="tableList"
                :data="tableData"
                style="width: 100%;height: 500px;"
                v-loading="tableLoading"
                show-overflow-tooltip
                empty-text="暂无数据"
                :header-cell-style="{ background: '#F5F7FA', color: '#333' }"
            >
                <el-table-column label="权益名称" min-width="150" >
                    <template #default="scope">
                        {{ scope.row.service.service_name }}
                    </template>
                </el-table-column>
                <el-table-column label="企业名称" prop="quantity" min-width="180" >
                    <template #default="scope">
                        {{ scope.row.extra_info ? JSON.stringify(scope.row.extra_info.companyName) : '-' }}
                    </template>
                </el-table-column>
                <el-table-column label="企业税号" prop="option_id" min-width="150" >
                    <template #default="scope">
                        {{ scope.row.extra_info ? scope.row.option_id : '-' }}
                    </template>
                </el-table-column>
                <el-table-column label="类型" prop="change_type" min-width="50">
                    <template #default="scope">
                        <span v-if="scope.row.change_type === 'prerevoke'">回退</span>
                        <span v-else-if="scope.row.change_type === 'deduct'">消费</span>
                        <span v-else-if="scope.row.change_type === 'prededuct'">预扣减</span>
                        <span v-else>其他</span>
                    </template>
                </el-table-column>
                <el-table-column label="数量" prop="quantity" min-width=“50” />
                <el-table-column label="操作员" prop="user_id" min-width=“100” />
                <el-table-column v-if="isPlatManager" label="所属租户" prop="channel_id" />
                <el-table-column label="消费时间" prop="created_at" min-width=“200” />
            </el-table>
            <el-affix position="bottom">
                <div class="pagination-bar">
                    <el-pagination
                        v-model:currentPage="pageInfo.page"
                        v-model:page-size="pageInfo.pageSize"
                        :total="pageInfo.total"
                        layout="total, sizes, prev, pager, next, jumper"
                        @change="pageChange"
                    />
                </div>
            </el-affix>    
        </div>
    </div>
</template>

<script lang='ts' setup>
import { ref, onMounted, computed, reactive } from 'vue'
import { useStore } from 'vuex'
import { useRoute } from 'vue-router'
import searchBox from '@/components/common/SearchBox.vue'
import type { RootState } from '@/types/store'
import orderService from '@/service/orderService'
import systemService from '@/service/systemService'
import type { IOrderUsageRecordParams, IOrderUsageRecordResponseItem } from '@/types/order'
import { ElMessage } from 'element-plus'

const isPlatManager = computed(() => {
    const { account } = store.state.user
    const { user } = account || {}
    const { tenantId } = user || {}
    return tenantId === ''
})

type CustomConfig = {
  [key: string]: Array<{
    label: string;
    value: string;
  }>;
}
const searchConfig = ref<CustomConfig>()
const searchOptionsKey = ref('BENEFIT_RECORD_SEARCH_OPTIONS')
const route = useRoute()
const tableLoading = ref<boolean>(false)
const exporting = ref(false)
const store = useStore<RootState>()
const userInfo = computed(() => {
    const { account } = store.state.user
    const { user } = account || {}
    return user
})
const totalNum = ref(0)
const pageInfo = reactive({
    page: 1,
    pageSize: 20,
    total: totalNum,
})

// const exportRecord = (num?: number) => {
// let params: IAutoDialerTaskExportRequest = {}

// if (!num) {
//     if (multipleSelection.value.length === 0) {
//         return ElMessage.warning('请选择要导出的记录')
//     }
//     params.ids = multipleSelection.value.map((item) => item.id)
// } else if (num > 0) {
//     params = { ...props.filterParams, nums: num }
// }

// if (exporting.value) return

// outboundService.taskExport(params)
//     .then((res) => {
//         if(res.data.type === 'application/vnd.ms-excel'){
//             exporting.value = true
//             downloadFile(res)
//             exporting.value = false
//         }
//         else{
//             ElMessage.success('您的导出任务已创建，请到任务管理页面查看下载')
//         }
//     })
//     .catch(() => {
//         exporting.value = false
//         ElMessage.warning('导出失败，请稍后再试')
//     })
// }

const queryParams = ref<IOrderUsageRecordParams>({
    page:pageInfo.page,
    pageSize:pageInfo.pageSize,
    serviceOrderId:'880'
})

const tableData = ref<IOrderUsageRecordResponseItem[]>([])

const getBenefitRecord = (Params: IOrderUsageRecordParams) => {
    tableLoading.value = true
    Params.page = pageInfo.page
    Params.pageSize = pageInfo.pageSize
    orderService.orderServiceUsagePage(Params).then((res) => {
        console.log(res)
        if(res.success){
            pageInfo.total = res.total
            tableData.value = res.data
        }else{
            ElMessage.error('系统错误')
        }
    }).then(() => {
        tableLoading.value = false
    })
}

const pageChange = (currentPage: number, currentPagesize: number) => {
    queryParams.value.page = currentPage
    queryParams.value.pageSize = currentPagesize
    getBenefitRecord(queryParams.value)
}

const updateSearchParams = (params: IOrderUsageRecordParams) =>{
    console.log('123123',params)
    if(params.status === '1'){
        delete queryParams.value.hideValid
        queryParams.value.hideExpired = 1
    }else if(params.status === '2'){
        delete queryParams.value.hideExpired
        queryParams.value.hideValid = 1
    }
    queryParams.value = params
    queryParams.value.serviceOrderId = '880'
    getBenefitRecord(queryParams.value)
}

let defaultQueryParams = reactive<Record<string, boolean | string | number[] | string[]>>({})
onMounted(() => {
    console.log(userInfo.value)

    if(isPlatManager.value){
        searchOptionsKey.value = 'BENEFIT_RECORD_SEARCH_OPTIONS_FORADMIN'
        systemService.tenantList().then(response => {
            searchConfig.value = {
                ...searchConfig.value,
                tenantId:response.map(item => ({
                    label:item.name,
                    value:item.id
                }))
            }
        })
    }

    systemService.userGetUserByScopeData('collect').then(response => {
        searchConfig.value = {
            ...searchConfig.value,
            createUser:response.data.map(item => ({ 
                value: item.id,
                label: item.nickname 
            }))
        }
    })

    if (route.query && JSON.stringify(route.query) !== '{}') {
        console.log('route.query', route.query)
        for (const key in route.query) {
            defaultQueryParams = {
                ...defaultQueryParams,
                [key]: route.query[key] as boolean | string | number[] | string[],
            }
        }
    } else {
        getBenefitRecord(queryParams.value)
    }
})

</script>

<style lang='scss' scoped>
@use '@/styles/element-lead.scss';

.pagination-bar {
    display: flex;
    flex-direction: row-reverse;
    width: 100%;
    background-color: #fff;
    padding-top: 16px;
    padding-bottom: 16px;
}
</style>