<template>
    <div class="theme-pic">
        <img class="pic" src="../../../assets/images/auth/theme_pic.png" alt="pic" />
    </div>
</template>

<script setup lang="ts"></script>

<style scoped>
.theme-pic {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
}

.pic {
    width: 500px;
    height: 500px;
}

@media screen and (max-width: 1600px) {
    .pic {
        width: 400px;
        height: 400px;
    }
}

@media screen and (max-width: 1200px) {
    .pic {
        width: 312px;
        height: 312px;
    }
}

@media screen and (max-width: 992px) {
    .pic {
        width: 256px;
        height: 256px;
    }
}

@media screen and (max-width: 768px) {
    .pic {
        width: 200px;
        height: 200px;
    }
}

@media screen and (max-width: 576px) {
    .pic {
        width: 150px;
        height: 150px;
    }
}
</style>
