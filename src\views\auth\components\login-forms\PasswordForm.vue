<template>
    <div class="password-form">
        <div class="title">账号登录</div>
        <div class="steps"></div>
        <div class="form">
            <el-form :model="passwordForm" ref="formRef" class="el-form">
                <el-form-item label="" class="password-form-item">
                    <el-input v-model="passwordForm.username" placeholder="登录账号" class="input" clearable />
                </el-form-item>
                <el-form-item label="" class="password-form-item">
                    <el-input
                        v-model="passwordForm.password"
                        placeholder="密码"
                        class="input"
                        show-password
                        clearable
                        @keyup.enter="submitForm()"
                    />
                </el-form-item>
                <div class="help">
                    <div @click="toForgot" class="pointer color-blue--hover">忘记密码？</div>
                </div>
            </el-form>
            <div class="btns">
                <el-button type="primary" round class="submit-btn" @click="submitForm()" :loading="logging">
                    登录
                </el-button>
            </div>
            <div id="captcha" class="captcha"></div>
        </div>

        <div class="tips">
            <div class="label">温馨提示：</div>
            <div class="desc">若无账号请联系后台管理员进行开通</div>
        </div>
    </div>
</template>

<script setup lang="ts">
import authService from '@/service/authService'
import type { IAuthLogin } from '@/types/auth'
import { loadScript } from '@/utils/loadJs'
import { ElMessage, type FormInstance } from 'element-plus'
import { onMounted, reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'
const logging = ref(false)
const store = useStore()
const router = useRouter()
const formRef = ref<FormInstance>()
const passwordForm = reactive({
    username: '',
    password: '',
})

onMounted(() => {
    loadScript('/static/tac/load.min.js')
})

const validate = () => {
    let flag = true
    const { username, password } = passwordForm || {}
    if (!username) {
        ElMessage.error('登录账号不能为空')
        flag = false
    }

    if (!password) {
        ElMessage.error('密码不能为空')
        flag = false
    }

    return flag
}

const submitForm = () => {
    if (logging.value) return
    if (!validate()) return

    const { username, password } = passwordForm || {}

    const credentials = {
        password: password,
        username: username.trim(),
    }

    logging.value = true

    authService
        .oauthOpenCaptcha({
            username,
        })
        .then((res) => {
            const { data } = res || {}
            if (data) {
                authService.oauthCaptcha(
                    credentials,
                    (credentials) => {
                        return login(credentials)
                    },
                    () => {
                        logging.value = false
                    }
                )
            } else {
                login(credentials)
            }
        })
        .catch(() => {
            logging.value = false
        })
}

const login = (credentials: IAuthLogin) => {
    authService
        .oauthToken(credentials)
        .then((res) => {
            logging.value = false
            const { errCode, data, errMsg } = res || {}
            if (errCode === 0) {
                const { auth } = data || {}
                store.dispatch('auth/loginSuccess', data)
                if (auth === 1) {
                    router.push('/')
                } else {
                    console.log('12321312')
                    console.log(auth)
                    router.push('/phone-bind')
                }
            } else if (errCode === 700) {
                // 弹出验证码
                authService.oauthCaptcha(
                    credentials,
                    (credentials) => {
                        return login(credentials)
                    },
                    () => {
                        logging.value = false
                    }
                )
            } else {
                ElMessage.error(errMsg)
            }
        })
        .catch((err) => {
            logging.value = false
            console.log(err)
        })
}

const toForgot = () => {
    router.push('forgot')
}
</script>

<style lang="scss" scoped>
@use './style.scss';
</style>
