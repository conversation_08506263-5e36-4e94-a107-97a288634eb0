<script lang='ts' setup>
import { ref, computed } from 'vue'
import { useStore } from 'vuex'
import crmService from '@/service/crmService'
import { ElMessage } from 'element-plus'
import type { IGetCrmLeadParams, ICrmExportParams } from '@/types/lead'
import type { RootState } from '@/types/store'
type FromValue = 'leadPool' | 'leadList' | 'customerPool' | 'customerList'
const props = defineProps<{
    checkedIds: string[]
    from: FromValue
    queryParams: IGetCrmLeadParams
}>()
const requestObj = ref<ICrmExportParams>({})
const exportData= ref<Blob>()
const dialogVisible = ref(false)
const fileName = ref('')
const store = useStore<RootState>()
const user = computed(() => {
    const { account } = store.state.user || {}
    const { user } = account || {}
    return user
})
const exportLoading = ref(false)
const handleExport = (val: string) => {
    if (val === 'checked' && props.checkedIds.length < 1) {
        return ElMessage.warning('请选择需要导出的线索')
    }

    // 点击导出的操作
    requestObj.value=JSON.parse(JSON.stringify(props.queryParams))
    if (val === 'checked') {
        requestObj.value.ids=props.checkedIds
        requestObj.value.exportType=props.from === 'leadList' ? 1 : 2
    } else {
        requestObj.value.exportNum = Number(val)
        requestObj.value.exportType = props.from === 'leadList' ? 1 : 2
    }
    
    crmService.crmExport(requestObj.value).then((res) => {
        console.log('导出的返回', res)
        // 判断 异步任务
        if (res.type === 'application/vnd.ms-excel') { 
            dialogVisible.value = true
            exportLoading.value = true
            exportData.value = res
            fileName.value = `${Date.now()}_${user.value?.nickname}_线索导出.xlsx`
            exportLoading.value = false
        } else {
            ElMessage.success('您的导出任务已创建，请到任务管理页面查看下载')
        }
       
    }).catch((error) => {
        exportLoading.value = false
        ElMessage.error(error)
    })
}
const handleDown = () => {
    if (exportData.value) {
        const blob = new Blob([exportData.value], { type: 'application/vnd.ms-excel' })
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.setAttribute('download', fileName.value)
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
        dialogVisible.value = false
    }
}
</script>
<template>
    <el-dropdown>
        <el-button class="no-focus-visible color-black">
            导出
            <el-icon class="el-icon--right">
                <CaretBottom />
            </el-icon>
        </el-button>
        <template #dropdown>
            <el-dropdown-menu>
                <el-dropdown-item @click="handleExport('checked')"><span class="color-black">导出所选</span></el-dropdown-item>
                <el-dropdown-item @click="handleExport('100')"><span class="color-black">导出前100条</span></el-dropdown-item>
                <el-dropdown-item @click="handleExport('500')"><span class="color-black">导出前500条</span></el-dropdown-item>
            </el-dropdown-menu>
        </template>
    </el-dropdown>
    <el-dialog
        v-model="dialogVisible"
        title="导出线索"
        width="500"
        show-close
        destroy-on-close
        style="padding: 16px 24px"
    >
        <div class="lr-padding-24" v-loading="exportLoading">
            <div class="tips b-margin-16">注:导出过程请不要关闭当前窗口或刷新页面</div>
            <div class="display-flex space-between content b-margin-8">
                <div>{{ fileName }}</div>
                <div class="!color-blue pointer" @click="handleDown">点击下载</div>
            </div>
        </div>
    </el-dialog>
</template>
<style scoped lang='scss'>
.tips{
    color: var(--three-grey);
    font-size: 14px;
}
.content{
    background-color: var(--card-bg);
    border-radius: 4px;
    padding: 8px 12px;
    color: --main-black;
}
</style>
