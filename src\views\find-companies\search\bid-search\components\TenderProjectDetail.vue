<template>
    <div>
        <el-dialog 
            title="项目详情"  
            width="1200px" 
            v-model="showDialog" 
            @close="closeDialog">
            <div v-loading="loading" style="min-height: 300px" >
                <div v-html="detailHtml"></div>
            </div>
        </el-dialog>
    </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, watch } from 'vue'
import aicService from '@/service/aicService'

const loading = ref<boolean>(false)
const detailHtml = ref('')
const props = defineProps<{
    ossId: string
    showTenderProjectDetail: boolean
}>()

const showDialog = ref(props.showTenderProjectDetail)
const closeDialog = () => {
    emit('update:showTenderProjectDetail', false)
}

watch(
    [() => props.showTenderProjectDetail, showDialog],
    ([newShowTenderProjectDetail, newShowDialog]) => {
        showDialog.value = newShowTenderProjectDetail
        emit('update:showTenderProjectDetail', newShowDialog)
    }
)

const getTenderDetail = async () => {
    loading.value = true
    aicService.searchGetTenderContents({ ossId: props.ossId }).then(res => {
        console.log(res)
        detailHtml.value = res
    }).finally(() => {
        loading.value = false
    })
}

// 定义emit对象
const emit = defineEmits(['update:showTenderProjectDetail'])

onMounted(() => {
    getTenderDetail()
})
</script>

<style lang="scss" scoped>

</style>