<template>
    <div class="back-color-white lr-padding-16 tb-padding-10">
        <el-breadcrumb separator="/">
            <el-breadcrumb-item v-for="item in breadcrumbs" :key="item.path" :to="{ path: item.path }">{{
                item.text
            }}</el-breadcrumb-item>
        </el-breadcrumb>
    </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()

const breadcrumbs = computed(() => {
    let breadcrumbArray: { text: string; path: string }[] = []
    const matchedRoutes = route.matched
    console.log('matchedRoutes')
    console.log(matchedRoutes)

    matchedRoutes.forEach((routeRecord) => {
        if (routeRecord.meta && routeRecord.meta.title) {
            breadcrumbArray.push({
                text: routeRecord.meta.title,
                path: routeRecord.path,
            })
        }
    })

    return breadcrumbArray
})
</script>

<style lang="sass" scoped></style>
