<template>
    <div class="display-flex flex-column height-100">
        <div class="cate-search-box display-flex top-bottom-center space-between">
            <div class="display-flex  top-bottom-center gap-16">
                <div>类目搜索:</div>
                <div>
                    <el-select v-model="firstCategoryId" filterable @change="firstCategoryChange()">
                        <el-option v-for="item in categoryList" :disabled="item.disabled" :key="item.id"
                                   :label="item.name" :value="item.id">
                            {{ item.name }}
                            <span v-if="item.disabled && !item.isNormal">(未开通)</span>
                        </el-option>
                    </el-select>
                </div>
                <div>
                    <el-select v-model="secondCategoryId" filterable clearable :filter-method="secondFilterMethod"
                               @change="secondCategoryChange" placeholder="二级目标">
                        <el-option v-for="item in secondMergeCategoryListCom" :key="item.id" :label="item.name"
                                   :value="item.id">{{ item.name }}</el-option>
                    </el-select>
                </div>
                <div style="font-size: 16px; align-content: center; margin-right: 10px; line-height: 32px">
                    模板搜索:
                </div>
                <div>
                    <el-input placeholder="搜索模板" @keyup.enter="getMarketTemplate()" v-model="searchModelVal"
                              clearable></el-input>
                </div>
            </div>
            <div>
                <el-button type="primary" @click="getMarketTemplate()">搜索</el-button>

            </div>
        </div>

        <el-row :gutter="16" class="t-margin-15 flex-grow-1" v-if="marketList.length">
            <el-col :xs="12" :sm="12" :md="8" :lg="6" :xl="4" v-for="item in marketList"
                    @mouseover="hoverMarketId = item.id" @mouseleave="hoverMarketId = ''" class="
                     b-margin-10" :key="item.id" @click="useMarket(item)">
                <div
                    class="market-item border border-radius-4 pointer justify-between top-bottom-center all-padding-16">
                    <div class="display-flex template-name">
                        <el-tooltip effect="dark" :content="item.name" placement="top">
                            <div class="flex-1 font-weight-500 font-first-title-active">
                                {{ item.name }}
                            </div>
                        </el-tooltip>

                        <div class="icon-box border-radius-4 display-flex left-right-center top-bottom-center"
                             :class="hoverMarketId == item.id ? 'back-color-blue' : ''">
                            <Icon icon="icon-icon_city" size="20px"
                                  :color="hoverMarketId == item.id ? commonColor.mainWhite : commonColor.mainBlue">
                            </Icon>
                        </div>
                    </div>
                    <div class="font-14 color-three-grey t-margin-24">使用次数:{{ item.useNum }}</div>
                </div>


            </el-col>
        </el-row>
        <el-skeleton class="t-margin-10" v-show="!templeteIsLoading" :rows="5" animated />

        <div v-if="!marketList.length && templeteIsLoading" class="flex-grow-1 top-bottom-center">
            <el-empty />
        </div>
        <div class="page-box display-flex justify-flex-end tb-padding-10">
            <el-pagination @change="getMarketTemplate()" v-model:page-size="templetePageInfo.pageSize"
                           :page-sizes="[10, 20, 30, 50, 100]" v-model:current-page="templetePageInfo.page" background
                           layout="total,sizes,prev, pager, next" :total="templetePageInfo.total" />
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUpdated, onUnmounted, getCurrentInstance, computed, reactive, defineProps, defineEmits, watch } from 'vue'
import type { Ref } from 'vue'
import aicService from '@/service/aicService'
import Icon from '@/components/common/Icon.vue'
import type { ISearchGetCategoryResponse, ISearchGetCategoryItem, ISearchGetTemplateResponse, ISearchGetTemplateItem } from '@/types/company'

const instance = getCurrentInstance()

const commonColor = instance?.appContext.config.globalProperties.$commom.color

const _lodash = instance?.appContext.config.globalProperties.$lodash


const hoverMarketId = ref<string>('')

//获取产业市场列表
const marketList: Ref<ISearchGetTemplateItem[]> = ref([])

const templetePageInfo = reactive({
    page: 1,
    pageSize: 20,
    total: 0
})

const firstCategoryId = ref<string>('') //一级分类
const secondCategoryId = ref<string>('') //二级分类
const searchModelVal = ref<string>('') //模板搜索变量

const categoryList: Ref<ISearchGetCategoryItem[]> = ref([])
const secondCategoryList: Ref<ISearchGetCategoryItem[]> = ref([])

const props = defineProps({
    outinFirstCategoryId: {
        type: String,
        default: ''
    }
})

const getCategoryList = () => {
    aicService.modelGetCategory({}).then((res: ISearchGetCategoryResponse) => {
        categoryList.value = res.data.sort((a, b) => {
            if (a.isNormal && !b.isNormal) {
                return -1
            } else if (!a.isNormal && b.isNormal) {
                return 1
            }
            if (!a.disabled && b.disabled) {
                return -1
            } else if (a.disabled && !b.disabled) {
                return 1
            }
            return 0
        })

        if (props.outinFirstCategoryId) {
            firstCategoryId.value = props.outinFirstCategoryId
            firstCategoryChange()
        } else {
            if (!categoryList.value[0].disabled) {
                firstCategoryId.value = categoryList.value[0].id
                if (firstCategoryId.value) {
                    firstCategoryChange()
                }
            } else {
                templeteIsLoading.value = true
            }
        }
    })
}

const secondMergeCategoryList = computed(() => {
    if (!secondCategoryList.value.length) {
        return []
    }

    const extractNames = (data: ISearchGetCategoryItem[]) => {
        return _lodash.flatMap(data, (item: ISearchGetCategoryItem) => [{ id: item.id, name: item.name, children: item.children }, ...(item.children ? extractNames(item.children) : [])])
    }

    let res = extractNames(secondCategoryList.value)

    return res.filter((item: ISearchGetCategoryItem) => {
        return item.id !== firstCategoryId.value
    })
})


const firstCategoryChange = () => {
    aicService.modelGetCategory({
        categoryId: firstCategoryId.value
    }).then((res: ISearchGetCategoryResponse) => {
        console.log(res.data)
        secondCategoryList.value = res.data
        secondCategoryId.value = ''
        templetePageInfo.page = 1
        marketList.value = []
        getMarketTemplate()
    })
}

const secondCategoryChange = () => {
    templetePageInfo.page = 1
    marketList.value = []
    getMarketTemplate()
}
const templeteIsLoading: Ref<boolean> = ref(false)
const getMarketTemplate = () => {
    if (!secondCategoryId.value && !firstCategoryId.value) {
        return
    }
    marketList.value = []
    templeteIsLoading.value = false
    aicService.searchGetTemplate({
        searchType: '1',
        templateCategoryId: secondCategoryId.value || firstCategoryId.value,
        templateName: searchModelVal.value,
        page: templetePageInfo.page,
        pageSize: templetePageInfo.pageSize
    }).then((res: ISearchGetTemplateResponse) => {
        marketList.value = res.data
        templetePageInfo.total = res.total
    }).finally(() => {
        templeteIsLoading.value = true
    })
}

const emits = defineEmits(['useMarket'])

const useMarket = (item: ISearchGetTemplateItem) => {
    aicService.searchUpdateTemplate({ templateId: item.id, useTag: true })
    emits('useMarket', item)
}

let secondMergeCategoryListCom: Ref<ISearchGetTemplateItem[]> = ref([])

watch(secondMergeCategoryList, (val: ISearchGetTemplateItem[]) => {
    secondMergeCategoryListCom.value = val
})

const secondFilterMethod = (val: string) => {
    if (!val) {
        secondMergeCategoryListCom.value = secondMergeCategoryList.value
    } else {
        secondMergeCategoryListCom.value = secondMergeCategoryList.value.filter((item: ISearchGetCategoryItem) => {
            if (item.name) {
                return item.name.includes(val)
            } else {
                return false
            }
        })
    }
}


onMounted(() => { getCategoryList() })
onUpdated(() => { })
onUnmounted(() => { })
</script>

<style lang='scss' scoped>
.cate-search-box {
    height: 50px
}

.market-item {
    height: 124px;

    .template-name {
        // height:
    }

    &:hover {
        border-color: var(--main-blue-);
    }

    .icon-box {
        width: 40px;
        height: 40px;
        box-shadow: 0px 2px 4px rgb(0, 0, 0, 0.1);
    }
}

.font-first-title-active {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    -webkit-line-clamp: 2;
    text-overflow: ellipsis;
    line-height: 1.5;
    height: 50px
}

.page-box {
    height: 50px;
}
</style>