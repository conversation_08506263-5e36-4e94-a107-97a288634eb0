<template>
    <div class="main">
        <div class="wrap">
            <div class="container">
                <div class="left">
                    <BrandTitle />
                    <ThemePic />
                    <Footer />
                </div>
                <div class="right">
                    <BrandTitle />
                    <Forms />
                    <Footer />
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { BrandTitle, Footer, ThemePic, Forms } from './components'
import { getItem } from '@/utils/storage'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'
import type { RootState } from '@/types/store'
const router = useRouter()
const store = useStore<RootState>()

const logout = async () => {
    store.dispatch('auth/logout')
}

const checkLogin = () => {
    const accessToken = getItem('access_token')
    if (accessToken) return router.push('/')
    logout()
}

onMounted(() => {
    checkLogin()
})
</script>

<style lang="scss" scoped>
.main {
    height: 100vh;
    width: 100%;
    margin: 0 auto;
    overflow: hidden;
}

.wrap {
    max-width: 1920px;
    height: 100%;
    margin: 0 auto;
    position: relative;
}

.container {
    max-width: 1320px;
    margin: 0 auto;
    display: flex;
    flex-direction: row;
    height: 100vh;
    justify-content: space-between;
}

.left,
.right {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.right {
    align-items: flex-end;
}

.right {
    justify-content: center;
}

.right .brand-title {
    display: none;
}

.right .footer {
    display: none;
}

/* 自适应宽度 */
@media screen and (max-width: 1600px) {
    .container {
        max-width: 1098px;
    }
}

@media screen and (max-width: 1200px) {
    .container {
        max-width: 822px;
    }
}

@media screen and (max-width: 992px) {
    .container {
        max-width: 675px;
    }
}

@media screen and (max-width: 768px) {
    .container {
        max-width: 522px;
    }
}

@media screen and (max-width: 576px) {
    .container {
        max-width: 395px;
    }
}
</style>
