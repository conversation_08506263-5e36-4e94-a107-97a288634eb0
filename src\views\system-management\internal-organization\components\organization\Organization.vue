<script setup lang="ts">
import systemService from '@/service/systemService'
import type { IOrgTreeItem, IOrgTreeRequest } from '@/types/org'
import { computed, onMounted, ref, watch } from 'vue'
import { debounce } from 'lodash'
import OrgUserTable from './components/OrgUserTable.vue'
import { OrgTree } from './components'
import { useStore } from 'vuex'
import type { RootState } from '@/types/store'
import OrgTreeForManage from './components/OrgTreeForManage.vue'
const input = ref('')
const list = ref<IOrgTreeItem[]>([])
const searchList = ref<IOrgTreeItem[] | null>(null)
const currentOrg = ref<IOrgTreeItem | null>(null)
const searching = ref(false)
const store = useStore<RootState>()

onMounted(() => {
    if (isPlatManager.value) return
    getOrgList()
})

const isPlatManager = computed(() => {
    const { account } = store.state.user
    const { user } = account || {}
    const { tenantId } = user || {}
    return tenantId === ''
})

const getOrgList = (id?: string) => {
    const params: IOrgTreeRequest = {}
    if (id) {
        params['tenantId'] = id
    }
    systemService.orgTree(params).then((res) => {
        const { errCode, data } = res
        if (errCode === 0) {
            list.value = data
        } else {
            list.value = []
        }
    })
}

const searchOrg = (v: string) => {
    if (!v) {
        searchList.value = null
        return
    }
    systemService
        .orgTree({ name: v })
        .then((res) => {
            const { errCode, data } = res
            if (errCode === 0) {
                searchList.value = data
            } else {
                searchList.value = []
            }
        })
        .finally(() => {
            searching.value = false
        })
}

const onSearch = debounce((v: string) => {
    searchOrg(v)
}, 500)

watch(
    () => input.value,
    (value) => {
        if (value) {
            searching.value = true
            onSearch(value)
        } else {
            searchList.value = []
        }
    }
)

const setCurrentOrg = (org: IOrgTreeItem | null) => {
    currentOrg.value = org
}

const searchOrgTenantId = (v: string) => {
    if (!v) {
        list.value = []
    } else {
        getOrgList(v)
    }
}
</script>

<template>
    <div class="flex flex-row height-100">
        <div class="flex flex-column all-padding-16 height-100 left-tree">
            <OrgTree
                :org-list="list"
                :search-list="searchList"
                :get-org-list="getOrgList"
                :search-org="searchOrg"
                :set-current-org="setCurrentOrg"
                v-if="!isPlatManager"
            />
            <OrgTreeForManage
                :org-list="list"
                :search-list="searchList"
                :get-org-list="getOrgList"
                :search-org="searchOrg"
                :search-org-by-id="searchOrgTenantId"
                :set-current-org="setCurrentOrg"
                v-if="isPlatManager"
            />
        </div>
        <div class="flex flex-column all-padding-16 flex-1 oh height-100">
            <OrgUserTable :org-list="list" :current-org="currentOrg" :set-current-org="setCurrentOrg" />
        </div>
    </div>
</template>

<style lang="scss" scoped>
.left-tree {
    border-right: 2px solid var(--el-border-color-light);
}
</style>
